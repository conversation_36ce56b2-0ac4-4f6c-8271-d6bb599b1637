"""
多代理PPO强化学习组件
类似于MMOA-RAG的多代理架构，用于训练attention机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class AgentConfig:
    """代理配置"""
    agent_id: str
    state_dim: int
    action_dims: Dict[str, int]
    hidden_dim: int = 256
    learning_rate: float = 3e-4


class AttentionAgent(nn.Module):
    """注意力代理 - 负责学习注意力权重分配"""

    def __init__(self, config: AgentConfig):
        super().__init__()
        self.config = config
        self.agent_id = config.agent_id

        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(config.state_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU()
        )

        # 注意力权重生成器
        self.attention_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.action_dims['attention_weights']),
            nn.Softmax(dim=-1)
        )

        # 价值函数
        self.value_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, 1)
        )

        # 动作参数（用于连续动作）
        self.log_std_params = nn.ParameterDict({
            name: nn.Parameter(torch.zeros(dim))
            for name, dim in config.action_dims.items()
            if name != 'attention_weights'
        })

    def forward(self, state: torch.Tensor) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """前向传播"""
        features = self.feature_extractor(state)

        # 生成注意力权重
        attention_weights = self.attention_head(features)

        # 生成其他动作参数
        action_params = {'attention_weights': attention_weights}

        # 价值估计
        value = self.value_head(features)

        return action_params, value

    def get_action_and_value(self, state: torch.Tensor, deterministic: bool = False) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor], torch.Tensor, torch.Tensor]:
        """获取动作和价值"""
        action_params, value = self.forward(state)

        actions = {}
        log_probs = {}
        entropy_sum = 0.0

        # 处理注意力权重（使用Categorical分布）
        attention_logits = torch.log(action_params['attention_weights'] + 1e-8)
        attention_dist = torch.distributions.Categorical(logits=attention_logits)

        if deterministic:
            attention_action = torch.argmax(attention_logits, dim=-1)
        else:
            attention_action = attention_dist.sample()

        actions['attention_weights'] = attention_action
        log_probs['attention_weights'] = attention_dist.log_prob(attention_action)
        entropy_sum += attention_dist.entropy().mean()

        # 处理其他连续动作
        for action_name, dim in self.config.action_dims.items():
            if action_name != 'attention_weights':
                mean = torch.zeros_like(value.squeeze(-1)).unsqueeze(-1).expand(-1, dim)
                log_std = self.log_std_params[action_name].expand_as(mean)
                std = torch.exp(log_std)
                dist = torch.distributions.Normal(mean, std)

                if deterministic:
                    action = mean
                else:
                    action = dist.sample()

                actions[action_name] = action
                log_probs[action_name] = dist.log_prob(action).sum(dim=-1)
                entropy_sum += dist.entropy().sum(dim=-1).mean()

        total_log_prob = torch.stack(list(log_probs.values())).sum(dim=0)

        return actions, log_probs, entropy_sum, value.squeeze(-1)

    def evaluate_actions(self, state: torch.Tensor, actions: Dict[str, torch.Tensor]) -> Tuple[Dict[str, torch.Tensor], torch.Tensor, torch.Tensor]:
        """评估动作"""
        action_params, value = self.forward(state)

        log_probs = {}
        entropy_sum = 0.0

        # 评估注意力权重
        attention_logits = torch.log(action_params['attention_weights'] + 1e-8)
        attention_dist = torch.distributions.Categorical(logits=attention_logits)
        log_probs['attention_weights'] = attention_dist.log_prob(actions['attention_weights'])
        entropy_sum += attention_dist.entropy().mean()

        # 评估其他连续动作
        for action_name, dim in self.config.action_dims.items():
            if action_name != 'attention_weights':
                mean = torch.zeros_like(value.squeeze(-1)).unsqueeze(-1).expand(-1, dim)
                log_std = self.log_std_params[action_name].expand_as(mean)
                std = torch.exp(log_std)
                dist = torch.distributions.Normal(mean, std)

                log_probs[action_name] = dist.log_prob(actions[action_name]).sum(dim=-1)
                entropy_sum += dist.entropy().sum(dim=-1).mean()

        return log_probs, entropy_sum, value.squeeze(-1)


class SelectorAgent(nn.Module):
    """选择器代理 - 负责特征选择和检索策略"""

    def __init__(self, config: AgentConfig):
        super().__init__()
        self.config = config
        self.agent_id = config.agent_id

        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(config.state_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU()
        )

        # 离散动作头（top-k选择）
        self.discrete_heads = nn.ModuleDict()
        for action_name, dim in config.action_dims.items():
            if 'discrete' in action_name:
                self.discrete_heads[action_name] = nn.Sequential(
                    nn.Linear(config.hidden_dim, config.hidden_dim),
                    nn.ReLU(),
                    nn.Linear(config.hidden_dim, dim)
                )

        # 连续动作头（权重调整）
        self.continuous_heads = nn.ModuleDict()
        for action_name, dim in config.action_dims.items():
            if 'continuous' in action_name:
                self.continuous_heads[action_name] = nn.Sequential(
                    nn.Linear(config.hidden_dim, config.hidden_dim),
                    nn.ReLU(),
                    nn.Linear(config.hidden_dim, dim)
                )

        # 价值函数
        self.value_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, 1)
        )

        # 连续动作的标准差参数
        self.log_std_params = nn.ParameterDict({
            name: nn.Parameter(torch.zeros(dim))
            for name, dim in config.action_dims.items()
            if 'continuous' in name
        })

    def forward(self, state: torch.Tensor) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """前向传播"""
        features = self.feature_extractor(state)

        action_params = {}

        # 离散动作参数
        for action_name, head in self.discrete_heads.items():
            action_params[action_name] = head(features)

        # 连续动作参数
        for action_name, head in self.continuous_heads.items():
            action_params[action_name] = head(features)

        # 价值估计
        value = self.value_head(features)

        return action_params, value

    def get_action_and_value(self, state: torch.Tensor, deterministic: bool = False) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor], torch.Tensor, torch.Tensor]:
        """获取动作和价值"""
        action_params, value = self.forward(state)

        actions = {}
        log_probs = {}
        entropy_sum = 0.0

        # 处理离散动作
        for action_name, params in action_params.items():
            if 'discrete' in action_name:
                dist = torch.distributions.Categorical(logits=params)
                if deterministic:
                    action = torch.argmax(params, dim=-1)
                else:
                    action = dist.sample()
                actions[action_name] = action
                log_probs[action_name] = dist.log_prob(action)
                entropy_sum += dist.entropy().mean()

        # 处理连续动作
        for action_name, params in action_params.items():
            if 'continuous' in action_name:
                mean = params
                log_std = self.log_std_params[action_name].expand_as(mean)
                std = torch.exp(log_std)
                dist = torch.distributions.Normal(mean, std)

                if deterministic:
                    action = mean
                else:
                    action = dist.sample()

                actions[action_name] = action
                log_probs[action_name] = dist.log_prob(action).sum(dim=-1)
                entropy_sum += dist.entropy().sum(dim=-1).mean()

        total_log_prob = torch.stack(list(log_probs.values())).sum(dim=0)

        return actions, log_probs, entropy_sum, value.squeeze(-1)

    def evaluate_actions(self, state: torch.Tensor, actions: Dict[str, torch.Tensor]) -> Tuple[Dict[str, torch.Tensor], torch.Tensor, torch.Tensor]:
        """评估动作"""
        action_params, value = self.forward(state)

        log_probs = {}
        entropy_sum = 0.0

        # 评估离散动作
        for action_name, params in action_params.items():
            if 'discrete' in action_name:
                dist = torch.distributions.Categorical(logits=params)
                log_probs[action_name] = dist.log_prob(actions[action_name])
                entropy_sum += dist.entropy().mean()

        # 评估连续动作
        for action_name, params in action_params.items():
            if 'continuous' in action_name:
                mean = params
                log_std = self.log_std_params[action_name].expand_as(mean)
                std = torch.exp(log_std)
                dist = torch.distributions.Normal(mean, std)

                log_probs[action_name] = dist.log_prob(actions[action_name]).sum(dim=-1)
                entropy_sum += dist.entropy().sum(dim=-1).mean()

        return log_probs, entropy_sum, value.squeeze(-1)


class MultiAgentPPOBuffer:
    """多代理PPO缓冲区"""

    def __init__(self, capacity: int = 10000):
        self.capacity = capacity
        self.agent_buffers = {}

    def add_agent(self, agent_id: str):
        """添加代理缓冲区"""
        self.agent_buffers[agent_id] = {
            'states': [],
            'actions': [],
            'log_probs': [],
            'values': [],
            'rewards': [],
            'dones': [],
            'advantages': [],
            'returns': [],
            'size': 0
        }

    def add_experience(self, agent_id: str, state, action, log_prob, value, reward, done):
        """添加经验"""
        if agent_id not in self.agent_buffers:
            self.add_agent(agent_id)

        buffer = self.agent_buffers[agent_id]

        if buffer['size'] >= self.capacity:
            # 移除最旧的经验
            for key in ['states', 'actions', 'log_probs', 'values', 'rewards', 'dones']:
                buffer[key].pop(0)
        else:
            buffer['size'] += 1

        buffer['states'].append(state)
        buffer['actions'].append(action)
        buffer['log_probs'].append(log_prob)
        buffer['values'].append(value)
        buffer['rewards'].append(reward)
        buffer['dones'].append(done)

    def compute_advantages(self, agent_id: str, gamma: float = 0.99, gae_lambda: float = 0.95):
        """计算GAE优势函数"""
        if agent_id not in self.agent_buffers:
            return

        buffer = self.agent_buffers[agent_id]
        if buffer['size'] == 0:
            return

        values = torch.stack(buffer['values'])
        rewards = torch.stack(buffer['rewards'])
        dones = torch.stack(buffer['dones'])

        advantages = []
        returns = []

        # 计算GAE
        gae = 0
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_value = 0
            else:
                next_value = values[t + 1]

            delta = rewards[t] + gamma * next_value * (1 - dones[t]) - values[t]
            gae = delta + gamma * gae_lambda * (1 - dones[t]) * gae
            advantages.insert(0, gae)

        # 计算回报
        for t in range(len(rewards)):
            returns.append(advantages[t] + values[t])

        buffer['advantages'] = torch.stack(advantages)
        buffer['returns'] = torch.stack(returns)

        # 标准化优势函数
        if len(buffer['advantages']) > 1:
            buffer['advantages'] = (buffer['advantages'] - buffer['advantages'].mean()) / (buffer['advantages'].std() + 1e-8)

    def get_batch(self, agent_id: str, batch_size: int):
        """获取批量数据"""
        if agent_id not in self.agent_buffers:
            return None

        buffer = self.agent_buffers[agent_id]
        if buffer['size'] == 0:
            return None

        indices = torch.randperm(buffer['size'])[:batch_size]

        batch_states = torch.stack([buffer['states'][i] for i in indices])
        batch_actions = {}
        batch_log_probs = {}

        # 处理动作和对数概率
        for i in indices:
            for action_name, action_value in buffer['actions'][i].items():
                if action_name not in batch_actions:
                    batch_actions[action_name] = []
                    batch_log_probs[action_name] = []
                batch_actions[action_name].append(action_value)
                batch_log_probs[action_name].append(buffer['log_probs'][i][action_name])

        # 转换为tensor
        for action_name in batch_actions:
            batch_actions[action_name] = torch.stack(batch_actions[action_name])
            batch_log_probs[action_name] = torch.stack(batch_log_probs[action_name])

        batch_values = torch.stack([buffer['values'][i] for i in indices])
        batch_advantages = buffer['advantages'][indices]
        batch_returns = buffer['returns'][indices]

        return batch_states, batch_actions, batch_log_probs, batch_values, batch_advantages, batch_returns

    def clear(self, agent_id: str = None):
        """清空缓冲区"""
        if agent_id is None:
            # 清空所有代理的缓冲区
            for agent_id in self.agent_buffers:
                self.clear(agent_id)
        else:
            if agent_id in self.agent_buffers:
                buffer = self.agent_buffers[agent_id]
                for key in ['states', 'actions', 'log_probs', 'values', 'rewards', 'dones', 'advantages', 'returns']:
                    buffer[key] = []
                buffer['size'] = 0


class MultiAgentCoordinator:
    """多代理协调器 - 管理代理间的协作和通信"""

    def __init__(self, agents: Dict[str, nn.Module]):
        self.agents = agents
        self.agent_ids = list(agents.keys())
        self.communication_network = self._build_communication_network()

    def _build_communication_network(self):
        """构建代理间通信网络"""
        # 简单的全连接通信网络
        num_agents = len(self.agents)
        return nn.ModuleDict({
            f"{agent_id}_comm": nn.Sequential(
                nn.Linear(256, 128),  # 假设特征维度为256
                nn.ReLU(),
                nn.Linear(128, 64)
            ) for agent_id in self.agent_ids
        })

    def coordinate_actions(self, states: Dict[str, torch.Tensor]) -> Dict[str, Dict[str, torch.Tensor]]:
        """协调多个代理的动作"""
        # 1. 获取各代理的初始动作
        agent_actions = {}
        agent_features = {}

        for agent_id, agent in self.agents.items():
            if agent_id in states:
                actions, _, _, _ = agent.get_action_and_value(states[agent_id])
                agent_actions[agent_id] = actions

                # 提取特征用于通信
                if hasattr(agent, 'feature_extractor'):
                    features = agent.feature_extractor(states[agent_id])
                    agent_features[agent_id] = features

        # 2. 代理间通信和协调
        coordinated_actions = self._apply_coordination(agent_actions, agent_features)

        return coordinated_actions

    def _apply_coordination(self, agent_actions: Dict[str, Dict[str, torch.Tensor]],
                          agent_features: Dict[str, torch.Tensor]) -> Dict[str, Dict[str, torch.Tensor]]:
        """应用协调机制"""
        coordinated_actions = {}

        # 计算通信消息
        comm_messages = {}
        for agent_id, features in agent_features.items():
            if agent_id in self.communication_network:
                comm_messages[agent_id] = self.communication_network[f"{agent_id}_comm"](features)

        # 基于通信消息调整动作
        for agent_id, actions in agent_actions.items():
            coordinated_actions[agent_id] = actions.copy()

            # 简单的协调策略：基于其他代理的通信消息调整动作
            if agent_id in comm_messages:
                # 这里可以实现更复杂的协调逻辑
                # 例如：注意力代理和选择器代理的协调
                pass

        return coordinated_actions


class MultiAgentRewardFunction:
    """多代理奖励函数 - 设计协作奖励机制"""

    def __init__(self, cooperation_weight: float = 0.3, individual_weight: float = 0.7):
        self.cooperation_weight = cooperation_weight
        self.individual_weight = individual_weight

    def compute_rewards(self,
                       agent_actions: Dict[str, Dict[str, torch.Tensor]],
                       predictions: torch.Tensor,
                       targets: torch.Tensor,
                       base_predictions: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """计算多代理奖励"""
        batch_size = predictions.size(0)

        # 1. 计算个体奖励（基于任务性能）
        individual_rewards = self._compute_individual_rewards(predictions, targets, base_predictions)

        # 2. 计算协作奖励（基于代理间协调）
        cooperation_rewards = self._compute_cooperation_rewards(agent_actions, batch_size)

        # 3. 组合奖励
        total_rewards = {}
        for agent_id in agent_actions.keys():
            total_rewards[agent_id] = (
                self.individual_weight * individual_rewards +
                self.cooperation_weight * cooperation_rewards.get(agent_id, torch.zeros_like(individual_rewards))
            )

        return total_rewards

    def _compute_individual_rewards(self, predictions: torch.Tensor, targets: torch.Tensor,
                                  base_predictions: torch.Tensor = None) -> torch.Tensor:
        """计算个体任务奖励"""
        # 基于准确率的奖励
        correct_predictions = (predictions.argmax(dim=-1) == targets).float()
        accuracy_reward = correct_predictions

        # 如果有基线预测，计算改进奖励
        if base_predictions is not None:
            base_correct = (base_predictions.argmax(dim=-1) == targets).float()
            improvement_reward = correct_predictions - base_correct
            return accuracy_reward + 0.5 * improvement_reward

        return accuracy_reward

    def _compute_cooperation_rewards(self, agent_actions: Dict[str, Dict[str, torch.Tensor]],
                                   batch_size: int) -> Dict[str, torch.Tensor]:
        """计算协作奖励"""
        cooperation_rewards = {}

        # 示例：鼓励注意力代理和选择器代理的协调
        attention_agents = [aid for aid in agent_actions.keys() if 'attention' in aid]
        selector_agents = [aid for aid in agent_actions.keys() if 'selector' in aid]

        # 计算注意力一致性奖励
        if len(attention_agents) > 1:
            attention_consistency = self._compute_attention_consistency(
                {aid: agent_actions[aid] for aid in attention_agents}
            )
            for aid in attention_agents:
                cooperation_rewards[aid] = attention_consistency

        # 计算选择器协调奖励
        if len(selector_agents) > 1:
            selector_coordination = self._compute_selector_coordination(
                {aid: agent_actions[aid] for aid in selector_agents}
            )
            for aid in selector_agents:
                cooperation_rewards[aid] = selector_coordination

        # 为没有协作奖励的代理分配零奖励
        for agent_id in agent_actions.keys():
            if agent_id not in cooperation_rewards:
                cooperation_rewards[agent_id] = torch.zeros(batch_size)

        return cooperation_rewards

    def _compute_attention_consistency(self, attention_actions: Dict[str, Dict[str, torch.Tensor]]) -> torch.Tensor:
        """计算注意力一致性奖励"""
        if len(attention_actions) < 2:
            return torch.zeros(1)

        # 提取注意力权重
        attention_weights = []
        for actions in attention_actions.values():
            if 'attention_weights' in actions:
                attention_weights.append(actions['attention_weights'])

        if len(attention_weights) < 2:
            return torch.zeros(1)

        # 计算注意力权重的一致性（使用余弦相似度）
        consistency_scores = []
        for i in range(len(attention_weights)):
            for j in range(i + 1, len(attention_weights)):
                similarity = F.cosine_similarity(
                    attention_weights[i].float(),
                    attention_weights[j].float(),
                    dim=-1
                )
                consistency_scores.append(similarity)

        if consistency_scores:
            return torch.stack(consistency_scores).mean(dim=0)
        else:
            return torch.zeros(attention_weights[0].size(0))

    def _compute_selector_coordination(self, selector_actions: Dict[str, Dict[str, torch.Tensor]]) -> torch.Tensor:
        """计算选择器协调奖励"""
        if len(selector_actions) < 2:
            return torch.zeros(1)

        # 提取离散动作（top-k选择）
        discrete_actions = []
        for actions in selector_actions.values():
            for action_name, action_value in actions.items():
                if 'discrete' in action_name:
                    discrete_actions.append(action_value)

        if len(discrete_actions) < 2:
            return torch.zeros(1)

        # 计算选择多样性奖励（鼓励不同的选择策略）
        diversity_scores = []
        for i in range(len(discrete_actions)):
            for j in range(i + 1, len(discrete_actions)):
                # 计算动作差异
                difference = (discrete_actions[i] != discrete_actions[j]).float()
                diversity_scores.append(difference)

        if diversity_scores:
            return torch.stack(diversity_scores).mean(dim=0)
        else:
            return torch.zeros(discrete_actions[0].size(0))


def compute_multi_agent_ppo_loss(agent: nn.Module,
                                states: torch.Tensor,
                                actions: Dict[str, torch.Tensor],
                                old_log_probs: Dict[str, torch.Tensor],
                                advantages: torch.Tensor,
                                returns: torch.Tensor,
                                clip_epsilon: float = 0.2,
                                value_coef: float = 0.5,
                                entropy_coef: float = 0.01) -> Tuple[torch.Tensor, Dict[str, float]]:
    """计算多代理PPO损失"""

    # 获取当前策略的输出
    new_log_probs, entropy, new_values = agent.evaluate_actions(states, actions)

    # 计算策略损失
    policy_losses = []
    for action_name in actions.keys():
        old_lp = old_log_probs[action_name]
        new_lp = new_log_probs[action_name]

        ratio = torch.exp(new_lp - old_lp)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - clip_epsilon, 1 + clip_epsilon) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        policy_losses.append(policy_loss)

    total_policy_loss = torch.stack(policy_losses).mean()

    # 计算价值损失
    value_loss = F.mse_loss(new_values, returns)

    # 计算总损失
    total_loss = total_policy_loss + value_coef * value_loss - entropy_coef * entropy

    # 返回损失信息
    loss_info = {
        'total_loss': total_loss.item(),
        'policy_loss': total_policy_loss.item(),
        'value_loss': value_loss.item(),
        'entropy': entropy.item()
    }

    return total_loss, loss_info
