"""
多代理PPO训练器
类似于MMOA-RAG的多代理强化学习训练框架
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional
from torch.utils.data import DataLoader
import logging
import gc
from tqdm import tqdm
import json
import faiss
from multi_agent_ppo import (
    AttentionAgent, SelectorAgent, MultiAgentCoordinator,
    MultiAgentRewardFunction, MultiAgentPPOBuffer,
    compute_multi_agent_ppo_loss, AgentConfig
)
from train_cot_Implicit import (
    HierarchicalAttentionModel,
    setup_logger,
    load_hdf5_to_dict,
    split_dict_into_chunks,
    transfer_chunks_to_gpus,
    create_key_to_gpu_map,
    build_faiss_index_and_shard
)
def _normalize(t: torch.Tensor) -> torch.Tensor:
    return (t - t.mean()) / (t.std() + 1e-8)

# Monkey‑patch buffer if not provided by original impl
if not hasattr(MultiAgentPPOBuffer, "_norm"):
    setattr(MultiAgentPPOBuffer, "_norm", staticmethod(_normalize))
class MultiAgentPPOTrainer:
    """多代理PPO训练器 - 类似于MMOA-RAG架构"""

    def __init__(self,
                 model: HierarchicalAttentionModel,
                 sft_weights_path: str,
                 unlabeled_loader: DataLoader = None,
                 learning_rate: float = 1e-4,
                 ppo_epochs: int = 6,
                 clip_epsilon: float = 0.1,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 buffer_size: int = 4096,
                 batch_size: int = 64,
                 cooperation_weight: float = 0.3):

        # 基础设置
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = model.to(self.device)
        self.sft_weights_path = sft_weights_path
        self.learning_rate = learning_rate
        self.ppo_epochs = ppo_epochs
        self.clip_epsilon = clip_epsilon
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.buffer_size = buffer_size
        self.batch_size = batch_size

        # 初始化日志
        self.logger = setup_logger("multi_agent_ppo_training_fixed.log")

        # 加载SFT权重
        self.load_sft_weights()

        # 初始化记忆库（如果提供了无标签数据）
        if unlabeled_loader is not None:
            self.init_memory_bank(unlabeled_loader)

        # 多代理缓冲区（需要在setup_multi_agent_ppo之前初始化）
        self.buffer = MultiAgentPPOBuffer(capacity=buffer_size)

        # 设置多代理PPO组件
        self.setup_multi_agent_ppo()

        # 多代理协调器
        self.coordinator = MultiAgentCoordinator(self.agents)

        # 多代理奖励函数
        self.reward_function = MultiAgentRewardFunction(
            cooperation_weight=cooperation_weight,
            individual_weight=1.0 - cooperation_weight
        )

        # 优化器
        self.setup_optimizers()

    def load_sft_weights(self):
        """加载SFT阶段的权重"""
        import os
        if os.path.exists(self.sft_weights_path):
            checkpoint = torch.load(self.sft_weights_path, map_location=self.device)
            self.model.load_state_dict(checkpoint)
            self.model = self.model.to(self.device)
            self.logger.info(f"成功加载SFT权重: {self.sft_weights_path}")
        else:
            self.logger.warning(f"SFT权重文件不存在: {self.sft_weights_path}")

    def init_memory_bank(self, unlabeled_loader):
        """初始化记忆库"""
        self.logger.info("开始初始化记忆库...")
        try:
            self.model.init_memory_bank(unlabeled_loader)
            self.logger.info("✅ 记忆库初始化完成")
        except Exception as e:
            self.logger.error(f"❌ 记忆库初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise e

    def setup_multi_agent_ppo(self):
        """设置多代理PPO组件"""
        self.agents = {}

        # 1. 为每个选择器创建代理
        for i, selector in enumerate(self.model.selectors):
            agent_id = f"selector_{i}"
            state_dim = 1024
            action_dims = {
                f"discrete_topk_{i}": 10,
                f"continuous_weight_{i}": 5
            }

            # 启用选择器PPO
            selector.enable_ppo(state_dim, action_dims)
            self.agents[agent_id] = selector.ppo_agent
            self.buffer.add_agent(agent_id)

            self.logger.info(f"为选择器{i}启用PPO代理")

        # 2. 为每个注意力层创建代理
        for i, attention_layer in enumerate(self.model.attention_layers):
            agent_id = f"attention_{i}"
            state_dim = 3 * 512 + 1  # 3*D + 1 (q_mean, k_mean, v_mean, similarity)
            action_dims = {
                "attention_weights": 197,  # 序列长度
                "attention_scale": 1
            }

            # 启用注意力PPO
            attention_layer.enable_attention_ppo(state_dim, action_dims)
            self.agents[agent_id] = attention_layer.attention_agent
            self.buffer.add_agent(agent_id)

            self.logger.info(f"为注意力层{i}启用PPO代理")

        self.logger.info(f"总共创建了 {len(self.agents)} 个PPO代理")

    def setup_optimizers(self):
        """设置优化器"""
        # 为每个代理创建独立的优化器
        self.optimizers = {}

        for agent_id, agent in self.agents.items():
            self.optimizers[agent_id] = optim.Adam(
                agent.parameters(),
                lr=self.learning_rate
            )

        # 收集所有代理的参数ID
        agent_param_ids = set()
        for agent in self.agents.values():
            for param in agent.parameters():
                agent_param_ids.add(id(param))

        # 主模型优化器（用于非PPO部分）
        main_model_params = []
        for param in self.model.parameters():
            if id(param) not in agent_param_ids:
                main_model_params.append(param)

        self.main_optimizer = optim.Adam(
            main_model_params,
            lr=self.learning_rate
        )

    def collect_experience(self, dataloader: DataLoader, num_episodes: int = 100):
        """收集多代理经验数据"""
        self.model.eval()
        total_batches = len(dataloader) if num_episodes is None else num_episodes
        episode_count = 0
    
        with torch.no_grad():
            for batch_idx, batch_data in tqdm(enumerate(dataloader),total=total_batches, desc="Collect"):
                if episode_count >= num_episodes:
                    break

                # 处理不同的数据格式
                if len(batch_data) == 3:
                    images, captions, labels = batch_data
                    image_cliptensor = {
                        'pixel_values': images.unsqueeze(1)
                    }
                elif len(batch_data) == 4:
                    images, captions, labels, image_cliptensor = batch_data
                else:
                    self.logger.warning(f"意外的批次数据格式，长度: {len(batch_data)}")
                    continue

                images = images.to(self.device)
                labels = labels.to(self.device)

                # 确保image_cliptensor格式正确
                if 'pixel_values' in image_cliptensor:
                    if image_cliptensor['pixel_values'].dim() == 5:
                        image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].squeeze(1)
                    image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].to(self.device)

                # 获取基础模型输出（用于计算奖励）
                try:
                    base_logits = self.model({
                        'image': images,
                        'text': captions
                    }, image_cliptensor, data_gpus, key_to_gpu, id2filename, index, current_epoch=0)
                except NameError:
                    # 如果全局变量不存在，跳过这个批次
                    self.logger.warning("缺少全局变量，跳过当前批次")
                    continue

                # 使用多代理PPO模式获取动作和价值
                ppo_logits, multi_agent_data = self.forward_multi_agent_ppo({
                    'image': images,
                    'text': captions
                }, image_cliptensor, current_epoch=0,deterministic=False)

                # 计算多代理奖励
                agent_actions = {agent_id: data['actions'] for agent_id, data in multi_agent_data.items() if data is not None}
                agent_rewards = self.reward_function.compute_rewards(
                    agent_actions, ppo_logits, labels, base_logits
                )

                # 存储经验到对应代理的缓冲区
                for agent_id, agent_data in multi_agent_data.items():
                    if agent_data is not None and agent_id in agent_rewards:
                        batch_size = agent_data['state'].size(0)
                        for i in range(batch_size):
                            self.buffer.add_experience(
                                agent_id=agent_id,
                                state=agent_data['state'][i],
                                action={k: v[i] for k, v in agent_data['actions'].items()},
                                log_prob={k: v[i] for k, v in agent_data['log_probs'].items()},
                                value=agent_data['value'][i],
                                reward=agent_rewards[agent_id][i],
                                done=torch.tensor(1.0)
                            )

                episode_count += 1

        # 为每个代理计算优势函数
        for agent_id in self.agents.keys():
            self.buffer.compute_advantages(agent_id)

        self.logger.info(f"收集了 {episode_count} 个episode的多代理经验")

    def forward_multi_agent_ppo(self, x: dict, image_cliptensor: dict, current_epoch: int = 0,deterministic=False):#更改：是否要deterministic？
        """多代理PPO前向传播"""
        # 初始特征提取
        img_feat = self.model.encoder.encode_image(x['image'])
        h = img_feat

        multi_agent_data = {}

        # 分层处理
        for layer_idx in range(self.model.num_layers):
            retrieval_stage = layer_idx % 4

            if layer_idx < 4:  # 只在特定层使用记忆库
                # 选择器代理
                selector_agent_id = f"selector_{layer_idx}"
                if hasattr(self.model.selectors[layer_idx], 'ppo_enabled') and self.model.selectors[layer_idx].ppo_enabled:
                    selected, selector_data = self.model.selectors[layer_idx].forward_ppo(
                        h, self.model.memory_bank, current_epoch, retrieval_stage,deterministic
                    )
                    multi_agent_data[selector_agent_id] = selector_data
                else:
                    selected = self.model.selectors[layer_idx](
                        h, self.model.memory_bank, current_epoch, retrieval_stage
                    )
                    multi_agent_data[selector_agent_id] = None

                # 注意力代理
                attention_agent_id = f"attention_{layer_idx}"
                if hasattr(self.model.attention_layers[layer_idx], 'ppo_enabled') and self.model.attention_layers[layer_idx].ppo_enabled:
                    if layer_idx == 0:
                        q_hid = img_feat
                    else:
                        q_hid = selected

                    h, attention_data = self.model.attention_layers[layer_idx].forward_ppo(
                        selected, h, h, q_hid,deterministic
                    )
                    multi_agent_data[attention_agent_id] = attention_data
                else:
                    if layer_idx == 0:
                        q_hid = img_feat
                    else:
                        q_hid = selected
                    h = self.model.attention_layers[layer_idx](selected, h, h, q_hid)
                    multi_agent_data[attention_agent_id] = None

                h = self.model.norm(h)
            else:
                h = self.model.attention_layers[layer_idx](h, h, h, q_hid)
                h = self.model.norm(h)
                multi_agent_data[f"attention_{layer_idx}"] = None

            # 残差连接
            if layer_idx > 0:
                h = h + h_prev
            h_prev = h

        # 分类
        h_pooled = torch.mean(h, dim=1)

        from retrival.retrieval_imageandiext import image_search
        try:
            # 尝试使用全局变量
            T = image_search(image_cliptensor, id2filename, data_gpus, key_to_gpu, index, k=11).to(h_pooled.device)
            f1 = self.model.text_processor(T)
            f1 = torch.mean(f1, dim=1)
            result = torch.cat((h_pooled, f1), dim=1)
        except NameError:
            # 如果全局变量不存在，使用零填充
            print("警告: PPO模式下缺少全局变量，使用零填充")
            f1 = torch.zeros(h_pooled.size(0), 512, device=h_pooled.device)
            result = torch.cat((h_pooled, f1), dim=1)

        logits = self.model.classifier(result)

        return logits, multi_agent_data

    def update_multi_agent_policy(self):
        """更新多代理策略"""
        self.model.train()
        total_loss = 0.0
        for epoch in range(self.ppo_epochs):
            # 为每个代理分别更新
            for agent_id, agent in self.agents.items():
                if self.buffer.agent_buffers[agent_id]['size'] > 0:
                    # 获取该代理的批量数据
                    batch_data = self.buffer.get_batch(agent_id, self.batch_size)
                    if batch_data is None:
                        continue

                    states, actions, old_log_probs, values, advantages, returns = batch_data

                    # 计算PPO损失
                    loss, loss_info = compute_multi_agent_ppo_loss(
                        agent=agent,
                        states=states,
                        actions=actions,
                        old_log_probs=old_log_probs,
                        advantages=advantages,
                        returns=returns,
                        clip_epsilon=self.clip_epsilon,
                        value_coef=self.value_coef,
                        entropy_coef=self.entropy_coef
                    )

                    # 反向传播
                    self.optimizers[agent_id].zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(agent.parameters(), self.max_grad_norm)
                    self.optimizers[agent_id].step()

                    total_loss += loss.item()
        # optional: update main model (e.g. KL to baseline)
        self.main_optimizer.zero_grad()
        # accumulate losses from agents if needed, here we just step to apply weight decay etc.
        self.main_optimizer.step()
        return total_loss / max(1, len(self.agents) * self.ppo_epochs)

    def train(self, train_dataloader: DataLoader, val_dataloader: DataLoader,
              num_iterations: int = 100, save_interval: int = 10):
        """多代理PPO训练主循环"""
        self.logger.info("开始多代理PPO训练...")

        best_accuracy = 0.0

        for iteration in range(num_iterations):
            self.logger.info(f"开始第 {iteration+1} 次多代理PPO迭代")

            # 1. 收集经验
            self.collect_experience(train_dataloader, num_episodes=50)

            # 2. 更新策略
            avg_loss = self.update_multi_agent_policy()

            # 3. 清空缓冲区
            self.buffer.clear()

            self.logger.info(f"多代理PPO更新完成，平均损失: {avg_loss:.4f}")

            # 4. 评估模型
            if (iteration + 1) % 5 == 0:
                accuracy = self.evaluate_multi_agent_model(val_dataloader)
                self.logger.info(f"迭代 {iteration+1}: 损失={avg_loss:.4f}, 准确率={accuracy*100:.2f}%")

                # 保存最佳模型
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    self.save_checkpoint(f"best_multi_agent_model_iter_{iteration+1}.pth")
                    self.logger.info(f"保存最佳模型: 准确率={accuracy*100:.2f}%")

            # 5. 定期保存检查点
            if (iteration + 1) % save_interval == 0:
                self.save_checkpoint(f"multi_agent_checkpoint_iter_{iteration+1}.pth")
                self.logger.info(f"保存检查点: iteration {iteration+1}")

    def evaluate_multi_agent_model(self, dataloader: DataLoader):
        """评估多代理模型"""
        self.model.eval()
        total_correct = 0
        total_samples = 0

        with torch.no_grad():
            for batch_data in tqdm(dataloader, desc="Evaluating"):
                # 处理不同的数据格式
                if len(batch_data) == 3:
                    images, captions, labels = batch_data
                    image_cliptensor = {
                        'pixel_values': images.unsqueeze(1)
                    }
                elif len(batch_data) == 4:
                    images, captions, labels, image_cliptensor = batch_data
                else:
                    continue

                images = images.to(self.device)
                labels = labels.to(self.device)

                # 确保image_cliptensor格式正确
                if 'pixel_values' in image_cliptensor:
                    if image_cliptensor['pixel_values'].dim() == 5:
                        image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].squeeze(1)
                    image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].to(self.device)

                # 前向传播
                logits, _ = self.forward_multi_agent_ppo({
                    'image': images,
                    'text': captions
                }, image_cliptensor, current_epoch=0,deterministic=True)

                # 计算预测结果
                _, predicted = torch.max(logits.data, 1)
                total_samples += labels.size(0)
                total_correct += (predicted == labels).sum().item()

        accuracy = total_correct / total_samples
        return accuracy

    def save_checkpoint(self, filename: str):
        """保存检查点"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'agents_state_dict': {agent_id: agent.state_dict() for agent_id, agent in self.agents.items()},
            'optimizers_state_dict': {agent_id: opt.state_dict() for agent_id, opt in self.optimizers.items()},
            'main_optimizer_state_dict': self.main_optimizer.state_dict(),
        }
        torch.save(checkpoint, filename)

    def load_checkpoint(self, filename: str):
        """加载检查点"""
        checkpoint = torch.load(filename, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])

        for agent_id, agent in self.agents.items():
            if agent_id in checkpoint['agents_state_dict']:
                agent.load_state_dict(checkpoint['agents_state_dict'][agent_id])

        for agent_id, optimizer in self.optimizers.items():
            if agent_id in checkpoint['optimizers_state_dict']:
                optimizer.load_state_dict(checkpoint['optimizers_state_dict'][agent_id])

        self.main_optimizer.load_state_dict(checkpoint['main_optimizer_state_dict'])

        self.logger.info(f"成功加载检查点: {filename}")


def main():
    """主训练函数"""
    print("开始多代理PPO训练...")

    # 加载数据
    print("加载数据...")
    try:
        # 加载数据
        file_path = "/data1/wangj/RAFT/Implicit_Text_cifar10_setting3.hdf5"
        data_dict = load_hdf5_to_dict(file_path)
        num_gpus = 3
        chunks = split_dict_into_chunks(data_dict, num_chunks=num_gpus)
        global data_gpus, key_to_gpu
        data_gpus = transfer_chunks_to_gpus(chunks, num_gpus=num_gpus)
        key_to_gpu = create_key_to_gpu_map(chunks, num_gpus=num_gpus)
        print("✅ 数据加载完成")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return

    # 清理内存
    del data_dict, chunks
    gc.collect()

    try:
        # 加载索引
        print("加载索引...")
        with open('/data1/wangj/RAFT/id2filename_setting3_weight.json', 'r') as file:
            global id2filename
            id2filename = json.load(file)

        index_cpu = faiss.read_index("/data1/wangj/RAFT/index_file_setting3_weight.faiss")
        global index
        index = build_faiss_index_and_shard(index_cpu)
        del index_cpu
        gc.collect()
        print("✅ 索引加载完成")
    except Exception as e:
        print(f"❌ 索引加载失败: {e}")
        return

    try:
        from train_longclip.sharegpt4v import share4v_train_dataset, share4v_val_dataset, share4v_unlabel_dataset
        from torch.utils.data import DataLoader

        trainset = share4v_train_dataset()
        train_dataloader = DataLoader(trainset, batch_size=16, shuffle=True)

        valset = share4v_val_dataset()
        val_dataloader = DataLoader(valset, batch_size=32, shuffle=False)

        print("✅ 数据加载完成")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return

    # 加载无标签数据用于记忆库初始化
    print("加载无标签数据...")
    try:
        unlabel_trainset = share4v_unlabel_dataset()
        unlabeled_loader = DataLoader(unlabel_trainset, batch_size=512, shuffle=False)
        print("✅ 无标签数据加载完成")
    except Exception as e:
        print(f"❌ 无标签数据加载失败: {e}")
        return

    # 创建模型
    print("创建模型...")
    try:
        from train_cot_Implicit import HierarchicalAttentionModel

        model = HierarchicalAttentionModel(num_classes=10)
        print("✅ 模型创建完成")
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return



    # 创建多代理PPO训练器
    print("创建多代理PPO训练器...")
    try:
        trainer = MultiAgentPPOTrainer(
            model=model,
            sft_weights_path="train_cot_unlabel_rat_Dantopk_imagetext_(4)(6)_4_pool_select1_LayerNorm_classlayer1_gpt_nok1_implicit_concat_512_rnn_setting2.pth",
            unlabeled_loader=unlabeled_loader,  # 传入无标签数据加载器
            learning_rate=1e-5,
            ppo_epochs=4,
            clip_epsilon=0.2,
            buffer_size=1024,
            batch_size=32,
            cooperation_weight=0.3
        )
        print("✅ 多代理PPO训练器创建完成")
    except Exception as e:
        print(f"❌ 多代理PPO训练器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return

    # 开始训练
    print("开始多代理PPO训练...")
    try:
        trainer.train(
            train_dataloader=train_dataloader,
            val_dataloader=val_dataloader,
            num_iterations=1000,
            save_interval=10
        )
        print("✅ 多代理PPO训练完成")
    except Exception as e:
        print(f"❌ 多代理PPO训练失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
