import json
import cv2
from PIL import Image
import clip
import sys
import torch
import torch.utils.data as data
import os
import numpy as np
import random
sys.path.append("/home/<USER>/RAFT/Long-CLIP-main")
from model import longclip
from transformers import CLIPModel, CLIPProcessor
data4v_root = '/data1/wangj/RAFT/output/'
json_name = 'output.json'
image_root = '/data1/wangj/RAFT/output/'

data4v_root_test = '/home/<USER>/RAFT/output_test/'
json_name_test = 'output.json'
image_root_test = '/home/<USER>/RAFT/output_test/'

#data4v_root_unlabel = '/data1/wangj/RAFT/output/'
#json_name_unlabel = 'output.json'
#image_root_unlabel = '/data1/wangj/RAFT/output/'
data4v_root_unlabel = '/data1/wangj/RAFT/output_unlabel/'
json_name_unlabel = 'output1.json'
image_root_unlabel = '/data1/wangj/RAFT/output_unlabel/'

#data4v_root_unlabel = '/data1/wangj/RAFT/output_unlabel_cot_gpt/'
#json_name_unlabel = 'output.json'
#image_root_unlabel = '/data1/wangj/RAFT/output_unlabel_cot_gpt/'

#data4v_root_unlabel = '/data1/wangj/RAFT/output_unlabel_setting3/'
#json_name_unlabel = 'output.json'
#image_root_unlabel = '/data1/wangj/RAFT/output_unlabel_setting3/'

'''
# 数据文件路径
json_path = data4v_root_unlabel + json_name_unlabel

# 输出目录（可修改）
output_dir = "./sentence_buckets"
os.makedirs(output_dir, exist_ok=True)

# 读取 JSON 数据
with open(json_path, 'r', encoding='utf8') as fp:
    json_data = json.load(fp)

# 创建空字典：{3: [...], 4: [...], ..., 20: [...]}
bucket_dict = {i: [] for i in range(0, 10)}

# 遍历数据集
for idx, entry in enumerate(json_data):
    try:
        caption = entry['conversations'][1]['value']
        sentences = caption.split("\n\n")
        n = len(sentences)
        if 0 <= n <= 10:
            # 重新拼接为原始文本形式，方便查看
            full_text = "\n\n".join(sentences).strip()
            bucket_dict[n].append(f"[ID: {entry.get('id', idx)}]\n{full_text}\n\n")
    except Exception as e:
        print(f"Skipping index {idx} due to error: {e}")

# 保存到对应文件
for n in range(0, 10):
    filename = os.path.join(output_dir, f"sentences{n}.txt")
    with open(filename, "w", encoding="utf-8") as f:
        f.writelines(bucket_dict[n])
    print(f"Saved {len(bucket_dict[n])} entries to {filename}")
'''
# 数据文件路径
json_path = data4v_root_unlabel + json_name_unlabel

# 输出目录（可修改）
output_dir = "./sentence_buckets"
os.makedirs(output_dir, exist_ok=True)

# 读取 JSON 数据
with open(json_path, 'r', encoding='utf8') as fp:
    json_data = json.load(fp)

# 创建空字典：{3: [...], 4: [...], ..., 20: [...]}
bucket_dict = {i: [] for i in range(0, 6)}

# 遍历数据集
for idx, entry in enumerate(json_data):
    try:
        caption = entry['conversations'][1]['value']
        sentences = caption.split("\n\n")
        n = len(sentences)
        if 0 <= n <= 5:
            # 重新拼接为原始文本形式，方便查看
            full_text = "\n\n".join(sentences).strip()
            bucket_dict[n].append(f"[ID: {entry.get('id', idx)}]\n{full_text}\n\n")
    except Exception as e:
        print(f"Skipping index {idx} due to error: {e}")

# 保存到对应文件
for n in range(0, 6):
    filename = os.path.join(output_dir, f"sentences{n}.txt")
    with open(filename, "w", encoding="utf-8") as f:
        f.writelines(bucket_dict[n])
    print(f"Saved {len(bucket_dict[n])} entries to {filename}")


class share4v_val_dataset(data.Dataset):
    def __init__(self):
        self.data4v_root = data4v_root_test
        self.json_name = json_name_test
        self.image_root = image_root_test
        with open(data4v_root_test + json_name_test, 'r',encoding='utf8')as fp:
            self.json_data = json.load(fp)
        _ , self.preprocess = longclip.load("/home/<USER>/RAFT/Long-CLIP-main/checkpoints/LongCLIP-B/longclip-B.pt", device='cpu')
        self.clipprocessor = CLIPProcessor.from_pretrained("/data1/wangj/clip-vit-base-patch32")
    def __len__(self):
        return len(self.json_data)

    def __getitem__(self, index):
        caption = self.json_data[index]['conversations'][1]['value']
        caption = caption.replace("\n", " ")
        sentences = caption.split("\n\n")  # 按段落拆分
        caption = "\n\n".join(sentences[1:])  # 重新拼接，去掉第一段
        image_name =self.json_data[index]['image']
        image = Image.open(image_name).convert("RGB")
        image_cliptensor=self.clipprocessor(images=image.resize((256, 256)),return_tensors="pt")
        image_tensor = self.preprocess(image)
        label = self.json_data[index].get('label', -1)  # 若无 label，则返回 -1 作为默认值
        label = torch.tensor(label, dtype=torch.long) 
        #return image_cliptensor,image_tensor, caption, label
        #return image_tensor,caption, label
        return image_tensor,caption, label,image_cliptensor


class share4v_train_dataset(data.Dataset):
    def __init__(self):
        self.data4v_root = data4v_root
        self.json_name = json_name
        self.image_root = image_root
        with open(data4v_root + json_name, 'r',encoding='utf8')as fp:
            self.json_data = json.load(fp)
        _ , self.preprocess = longclip.load("/home/<USER>/RAFT/Long-CLIP-main/checkpoints/LongCLIP-B/longclip-B.pt", device='cpu')
        self.clipprocessor = CLIPProcessor.from_pretrained("/data1/wangj/clip-vit-base-patch32")
    def __len__(self):
        return len(self.json_data)

    def __getitem__(self, index):
        caption = self.json_data[index]['conversations'][1]['value']
        #caption = caption.replace("\n", " ")
        sentences = caption.split("\n\n")  # 按段落拆分
        caption = "\n\n".join(sentences[1:])  # 重新拼接，去掉第一段


        caption_short = caption.split(". ")[0]
        
        image_name = self.json_data[index]['image']
        image = Image.open(image_name).convert("RGB")
        image_cliptensor=self.clipprocessor(images=image.resize((256, 256)),return_tensors="pt")
        image_tensor = self.preprocess(image)
        label = self.json_data[index].get('label', -1)  # 若无 label，则返回 -1 作为默认值
        label = torch.tensor(label, dtype=torch.long) 
        #return image_cliptensor,image_tensor, caption, label
        #return image_tensor,caption, label
        return image_tensor,caption, label, image_cliptensor
            
class share4v_unlabel_dataset(data.Dataset):
    def __init__(self):
        self.data4v_root = data4v_root_unlabel
        self.json_name = json_name_unlabel
        self.image_root = image_root_unlabel
        self.skip_count = 0
        with open(self.data4v_root + self.json_name, 'r',encoding='utf8')as fp:
            self.json_data = json.load(fp)
        _ , self.preprocess = longclip.load("/home/<USER>/RAFT/Long-CLIP-main/checkpoints/LongCLIP-B/longclip-B.pt", device='cpu')
        self.clipprocessor = CLIPProcessor.from_pretrained("/data1/wangj/clip-vit-base-patch32")
    def __len__(self):
        return len(self.json_data)

    def __getitem__(self, index):
        caption = self.json_data[index]['conversations'][1]['value']
        #caption = caption.replace("\n", " ")
        sentences = caption.split("\n\n")  # 按段落拆分
        if len(sentences) < 5:
            self.skip_count = self.skip_count+1
            print("skip_count:",self.skip_count)
            new_index = (index + 1) % len(self.json_data)
            return self.__getitem__(new_index)
        #print(len(sentences))
        sentences=sentences[1:5]
        #caption = "\n\n".join(sentences[1:])  # 重新拼接，去掉第一段
        caption_short = caption.split(". ")[0]
        image_name = self.json_data[index]['image']
        image = Image.open(image_name).convert("RGB")
        image_cliptensor=self.clipprocessor(images=image.resize((256, 256)),return_tensors="pt")
        image_tensor = self.preprocess(image)
        return image_tensor,sentences
        #return image_tensor
        #return caption