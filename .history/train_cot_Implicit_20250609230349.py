import torch
import torchvision
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision.datasets import CIFAR10
import torchvision.models as models
from PIL import Image
import numpy as np
import faiss
from transformers import CLIPModel, CLIPProcessor
import torch
import numpy as np
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import os
from tqdm import tqdm
from sklearn.decomposition import PCA
import h5py
import numpy as np
import pickle
import torch
from torch.utils.data import Dataset
from torch.utils.data import Subset, DataLoader
import hnswlib
import json
import gc
from retrival.BM25_retrieval_imageandtext import batch_search
from retrival.retrieval_imageandiext import image_search,get_value_by_key
from torchvision.transforms import ToPILImage
from randaugment import RandAugment
import random
import logging
from peft import LoraConfig, get_peft_model
import sys
import torch.nn.functional as F
sys.path.append("/home/<USER>/RAFT/Long-CLIP-main")
#print("Current sys.path:", sys.path)
from train_longclip.sharegpt4v import share4v_val_dataset, share4v_train_dataset,share4v_unlabel_dataset
from model import longclip
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def load_hdf5_to_dict(file_path):
       with h5py.File(file_path, 'r') as h5f:
           data_dict = {}
           for key in h5f.keys():
               data_dict[key] = h5f[key][()]
       return data_dict
def split_dict_into_chunks(data_dict, num_chunks=3):
    """
    将字典分割成指定数量的子字典。

    Args:
        data_dict (dict): 需要分割的原始字典。
        num_chunks (int): 分割成多少个子字典。

    Returns:
        list of dict: 包含分割后子字典的列表。
    """
    keys = sorted(data_dict.keys())  # 排序键，确保分割一致性
    total_keys = len(keys)
    chunk_size = total_keys // num_chunks
    chunks = []
    for i in range(num_chunks):
        start_idx = i * chunk_size
        # 确保最后一块包含所有剩余的键
        end_idx = (i + 1) * chunk_size if i < num_chunks - 1 else total_keys
        chunk_keys = keys[start_idx:end_idx]
        chunk_dict = {k: data_dict[k] for k in chunk_keys}
        chunks.append(chunk_dict)
        print(f"Chunk {i} size: {len(chunk_dict)} keys")
    return chunks
def transfer_chunks_to_gpus(chunks, num_gpus=3):
    """
    将分割后的数据转移到指定的 GPU 上。

    Args:
        chunks (list of dict): 分割后的子字典列表。
        num_gpus (int): 使用的 GPU 数量。

    Returns:
        list of dict: 包含转移到 GPU 的子字典列表。
    """
    data_gpus = []
    for i in range(num_gpus):
        gpu_id = i % torch.cuda.device_count()  # 确保 GPU ID 在可用范围内
        chunk = chunks[i]
        # 转移每个值到对应的 GPU，并存储在新的字典中
        gpu_dict = {}
        for key, value in chunk.items():
            tensor = torch.tensor(value).cuda(gpu_id)
            gpu_dict[key] = tensor
        data_gpus.append(gpu_dict)
        print(f"Transferred chunk {i} to GPU {gpu_id}")
    return data_gpus
def create_key_to_gpu_map(chunks, num_gpus=3):
    """
    创建一个键到 GPU 的映射字典。

    Args:
        chunks (list of dict): 分割后的子字典列表。
        num_gpus (int): GPU 的数量。

    Returns:
        dict: 键到 GPU ID 的映射字典。
    """
    key_to_gpu = {}
    for i in range(num_gpus):
        gpu_id = i % torch.cuda.device_count()
        for key in chunks[i].keys():
            key_to_gpu[key] = gpu_id
    print("Created key to GPU mapping.")
    return key_to_gpu
#file_path = "/data1/wangj/CLIPCAP/prefix_embed_cifar10_setting1.hdf5"    #setting1_noaug
#file_path = "/data1/wangj/CLIPCAP/prefix_embed_cifar10_original.hdf5"    #setting1
#file_path = "/data1_hdd/chenglc/wangj/Implicit_Text_cifar10_original.hdf5"
#file_path = "/data1_hdd/chenglc/wangj/Implicit_Text_cifar10_setting2.hdf5"
file_path = "/data1/wangj/RAFT/Implicit_Text_cifar10_setting3.hdf5"
#file_path = "/data1/wangj/CLIPCAP/prefix_embed_cifar10_setting2.hdf5"   #setting2
#file_path = "/data1/wangj/CLIPCAP/prefix_embed_stl10_setting3.hdf5"    #setting3
data_dict = load_hdf5_to_dict(file_path)
num_gpus = 3
chunks = split_dict_into_chunks(data_dict, num_chunks=num_gpus)
data_gpus = transfer_chunks_to_gpus(chunks, num_gpus=num_gpus)
key_to_gpu = create_key_to_gpu_map(chunks, num_gpus=num_gpus)
# 删除不再需要的变量
del data_dict
del chunks
gc.collect()
# ==================== 基础组件 ====================
class LongCLIPEncoder(nn.Module):
    """简化版特征提取器"""
    def __init__(self, feat_dim=512):
        super().__init__()
        self.device = device
        self.model, self.preprocess = longclip.load("/home/<USER>/RAFT/Long-CLIP-main/checkpoints/LongCLIP-B/longclip-B.pt", device='cpu')
        config = LoraConfig(
            r=8,
            lora_alpha=8,
            target_modules=["resblocks.11.attn.out_proj"],
            task_type="SEQ_CLS",
            lora_dropout=0.05,
            bias="none",
            inference_mode=False,
            use_rslora=True,
            init_lora_weights="gaussian",
            revision='refs/pr/6'
            )

        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        self.model = self.model.to(self.device)  # 确保模型在正确的设备上
        self.dropout = nn.Dropout(p=0.5)  # 丢弃概率为 0.5
    def encode_image(self, images):
        images = images.to(self.device)
        with torch.no_grad():
            image_feature = self.model.encode_image(images)
            image_feature = image_feature/image_feature.norm(dim=-1, keepdim=True)
        image_feature = self.dropout(image_feature)
        return image_feature

    def encode_text(self, texts):
        texts = longclip.tokenize(texts,truncate=True).to(self.device)
        with torch.no_grad():
            texts_feature = self.model.encode_text_full(texts)  # embed with text encoder
            texts_feature = texts_feature/texts_feature.norm(dim=-1, keepdim=True)
        texts_feature = self.dropout(texts_feature)
        return texts_feature

class LearnablePruning(nn.Module):
    def __init__(self, feature_dim, num_keep_tokens=197):
        super().__init__()
        self.num_keep_tokens = num_keep_tokens
        self.scoring_mlp = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        scores = self.scoring_mlp(x).squeeze(-1)  # (batch_size, num_tokens)
        _, topk_indices = torch.topk(scores, self.num_keep_tokens, dim=-1, sorted=True)
        pruned_tokens = torch.gather(x, 1, topk_indices.unsqueeze(-1).expand(-1, -1, x.shape[-1]))

        return pruned_tokens
import faiss

class FaissMemoryBank:
    def __init__(self, feat_dim=512, capacity=50000, use_gpu=False):
        self.feat_dim = feat_dim
        self.capacity = capacity
        self.cpu_indexes = [None, None, None, None]  # 三个独立的索引
        self.gpu1_indexes = [None, None, None, None]
        self.gpu2_indexes = [None, None, None, None]
        self.gpu3_indexes = [None, None, None, None]
        self.shards_index= [None, None, None, None]
        self.features_list = [[], [], [], []]  # 存储三个特征组的列表
        self.use_gpu = use_gpu

        # 初始化Faiss索引
        for i in range(4):
            self.reset_index(i)

    def reset_index(self, idx):
        """初始化或重置指定索引"""
        if self.cpu_indexes[idx] is not None:
            del self.cpu_indexes[idx]
        if self.gpu1_indexes[idx] is not None:
            del self.gpu1_indexes[idx]
        if self.gpu2_indexes[idx] is not None:
            del self.gpu2_indexes[idx]
        if self.gpu3_indexes[idx] is not None:
            del self.gpu3_indexes[idx]

        self.cpu_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu1_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu2_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu3_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        if self.use_gpu:
            res1 = faiss.StandardGpuResources()
            res2 = faiss.StandardGpuResources()
            res3 = faiss.StandardGpuResources()
            self.gpu1_indexes[idx] = faiss.index_cpu_to_gpu(res1, 1, self.gpu1_indexes[idx])
            self.gpu2_indexes[idx] = faiss.index_cpu_to_gpu(res2, 2, self.gpu2_indexes[idx])
            self.gpu3_indexes[idx] = faiss.index_cpu_to_gpu(res3, 3, self.gpu3_indexes[idx])

        self.features_list[idx] = np.zeros((0, self.feat_dim), dtype='float32')

    def add(self, features_list):
        """添加三组特征到记忆库"""
        for i in range(4):
            features = features_list[i]
            #print(f"Adding features to index {i}: {features.shape}")
            if isinstance(features, torch.Tensor):
                features = features.cpu().numpy().astype('float32')

            # 检查容量
            #if len(self.features_list[i]) + len(features) > self.capacity:
            #    self._remove_oldest(i, self.capacity - len(features))
            try:
                self.gpu1_indexes[i].add(features.reshape(-1, self.feat_dim))
            except RuntimeError as e:
                # 假设这里捕获“显存不足”的错误
                print("GPU1 内存不足，退回到 GPU2 索引")
                try:
                    self.gpu2_indexes[i].add(features.reshape(-1, self.feat_dim))
                except RuntimeError as e:
                    print("GPU2 内存不足，退回到 GPU3 索引")
                    try:
                        self.gpu3_indexes[i].add(features.reshape(-1, self.feat_dim))
                    except RuntimeError as e:
                        print("GPU3 内存不足，退回到 CPU 索引")
                        self.cpu_indexes[i].add(features.reshape(-1, self.feat_dim))
            self.features_list[i] = np.vstack([self.features_list[i].astype('float16') , features.reshape(-1, self.feat_dim).astype('float16') ])
            #print(f"添加特征到索引 {i}: {features.shape}")
            #print(f"当前特征列表大小: {self.features_list[i].shape}")
    def _remove_oldest(self, idx, num_to_keep):
        """FIFO淘汰旧特征"""
        self.features_list[idx] = self.features_list[idx][-num_to_keep:]
        self.reset_index(idx)
        self.indexes[idx].add(self.features_list[idx])

    def shareindex(self):
        for i in range(4):
            self.shards_index[i] = faiss.IndexShards(self.feat_dim)
                # 把 GPU 索引（已经在显存中）和 CPU 索引都加进去
            self.shards_index[i].addIndex(self.gpu1_indexes[i])
            #self.shards_index[i].addIndex(self.gpu2_indexes[i])
            #self.shards_index[i].addIndex(self.gpu3_indexes[i])
            #self.shards_index[i].addIndex(self.cpu_indexes[i])


    def search(self, queries, k=5,stage=0):
        """
        搜索最相似的k个特征
        输入: queries [B, D]
        返回:
            distances: [B, k]
            indices: [B, k]
        """
        if isinstance(queries, torch.Tensor):
            queries = queries.cpu().numpy().reshape(-1, self.feat_dim).astype('float32')

        distances, indices = self.shards_index[stage].search(queries.reshape(-1, self.feat_dim), k)
        return distances, indices

    def sample(self, queries, k=5):
        """结合相似度和随机性的采样"""
        # 1. 计算相似度
        distances, indices = self.search(queries, k*3)  # 多取一些候选

        # 2. 随机选择
        selected = []
        for i in range(len(queries)):
            # 从top 3k中随机选k个
            idx = np.random.choice(indices[i], k, replace=False)
            selected.append(self.features[idx])

        return torch.from_numpy(np.stack(selected)).float()

# ==================== 核心模块 ====================
class DynamicSelector(nn.Module):
    def __init__(self, feat_dim, initial_topk=5):
        super().__init__()
        self.feat_dim = feat_dim
        self.topk = initial_topk

        # 评分网络 (现在直接在CPU上运行)
        self.scorer = nn.Sequential(
            nn.Linear(feat_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )  # 明确放在CPU上

        # PPO相关组件
        self.ppo_enabled = False
        self.ppo_agent = None

    def update_topk(self, new_topk):
        """课程学习调整选择数量"""
        self.topk = new_topk

    def enable_ppo(self, state_dim, action_dims, hidden_dim=256):
        """启用PPO模式"""
        self.ppo_enabled = True
        from ppo_components import PPOAgent
        self.ppo_agent = PPOAgent(state_dim, action_dims, hidden_dim)

        # 确保PPO智能体在正确的设备上
        device = next(self.scorer.parameters()).device
        self.ppo_agent = self.ppo_agent.to(device)

    def forward(self, query_feats, memory_bank, current_epoch, retrieval_stage=0, ppo_mode=False, deterministic=False):
        device = query_feats.device #[B, N, D]
        print(f"Shape of image_feature: {query_feats.shape}")
        B, N, D = query_feats.shape # B=32, N=197, D=512

        # PPO模式下的处理
        if ppo_mode and self.ppo_enabled and self.ppo_agent is not None:
            return self.forward_ppo(query_feats, memory_bank, current_epoch, retrieval_stage, deterministic)

        # 1. 使用Faiss进行初步筛选 (在CPU/GPU上)
        I, topk_indices = memory_bank.search(query_feats.detach().cpu().numpy(), k=self.topk*5,stage=retrieval_stage)
        print(f"Faiss返回的topk_indices shape: {topk_indices.shape}")  # [B*N, topk*5] 假设topk=12→[6304,60]
        # 2. 获取候选特征 [B, topk*5, D]
        candidate_feats = torch.stack([
            torch.from_numpy(memory_bank.features_list[retrieval_stage][indices]).to(device)
            for indices in topk_indices
        ])
        print(f"Stack后的candidate_feats shape: {candidate_feats.shape}")  # [B*N, topk*5, D] [6304,60,512]
        candidate_feats = candidate_feats.view(B, N, self.topk*5, D)
        print(f"View调整后的candidate_feats shape: {candidate_feats.shape}")  # [B, N, topk*5, D] [32,197,60,512]
        # 3. 精细评分 (小批量处理)
        batch_size = 32
        scores = []
        # 确保candidate_feats是float32类型
        candidate_feats = candidate_feats.float()
        for i in range(0, len(candidate_feats), batch_size):
            batch = candidate_feats[i:i+batch_size]   # [min(batch_size,B), N, topk*5, D]
            scores.append(self.scorer(batch).squeeze(-1)) # 假设scorer输出[B,N,topk*5,1]→[B,N,topk*5]
            torch.cuda.empty_cache()

        scores = torch.cat(scores)   # [B, N, topk*5]
        print(f"合并后的scores shape: {scores.shape}")  # [32,197,60]


        # 4. 最终选择
        _, selected_indices = torch.topk(scores, self.topk, dim=-1) # [B, N, topk] [32,197,12]
        print(f"selected_indices shape: {selected_indices.shape}")
        selected = torch.gather(
            candidate_feats.float().to(device), # [32,197,60,512]
            2, # 在dim=2(topk*5)上gather
            selected_indices.unsqueeze(-1).expand(-1, -1, -1, D) # [32,197,12,512]
        )
        print(f"gather后的selected shape: {selected.shape}")  # [32,197,12,512]
        averaged_selected=torch.mean(selected, dim=2)   # [32,197,512]
        print(f"平均后的输出shape: {averaged_selected.shape}")
        return averaged_selected

    def forward_ppo(self, query_feats, memory_bank, current_epoch, retrieval_stage=0, deterministic=False):
        """PPO模式的前向传播"""
        device = query_feats.device
        B, N, D = query_feats.shape

        # 1. 使用Faiss进行初步筛选
        I, topk_indices = memory_bank.search(query_feats.detach().cpu().numpy(), k=self.topk*5, stage=retrieval_stage)

        # 2. 获取候选特征
        candidate_feats = torch.stack([
            torch.from_numpy(memory_bank.features_list[retrieval_stage][indices]).to(device)
            for indices in topk_indices
        ])
        candidate_feats = candidate_feats.view(B, N, self.topk*5, D)

        # 3. 准备PPO状态
        state = self.prepare_ppo_state(query_feats, candidate_feats)

        # 4. 使用PPO智能体选择动作
        actions, log_probs, entropy, value = self.ppo_agent.get_action_and_value(state, deterministic)

        # 5. 根据动作选择特征
        selected_feats = self.apply_ppo_actions(candidate_feats, actions)

        # 返回选择的特征和PPO数据
        ppo_data = {
            'state': state,
            'actions': actions,
            'log_probs': log_probs,
            'value': value,
            'entropy': entropy
        }

        return selected_feats, ppo_data

    def prepare_ppo_state(self, query_feats, candidate_feats):
        """准备PPO状态表示"""
        B, N, D = query_feats.shape

        # 计算查询特征和候选特征的统计信息
        query_mean = torch.mean(query_feats, dim=1)  # [B, D]
        candidate_mean = torch.mean(candidate_feats, dim=(1, 2))  # [B, D]

        # 组合状态
        state = torch.cat([query_mean, candidate_mean], dim=-1)  # [B, 2*D]

        return state

    def apply_ppo_actions(self, candidate_feats, actions):
        """根据PPO动作选择特征"""
        B, N, num_candidates, D = candidate_feats.shape

        # 从动作中提取选择信息
        # 处理批次数据，每个样本可能有不同的topk选择
        if 'discrete_topk_0' in actions:
            # actions['discrete_topk_0'] 是 [B] 形状的tensor
            discrete_actions = actions['discrete_topk_0']  # [B]
            # 为了简化，使用批次中的平均值
            avg_topk = int(discrete_actions.float().mean().item()) + 1
            topk = min(avg_topk, num_candidates, self.topk)
        else:
            topk = self.topk

        # 使用原有的评分机制，但结合PPO的权重调整
        # 确保candidate_feats是float32类型
        candidate_feats = candidate_feats.float()
        scores = self.scorer(candidate_feats).squeeze(-1)  # [B, N, num_candidates]

        # 如果有连续动作，用于调整分数
        if 'continuous_weight_0' in actions:
            weight_adjustment = actions['continuous_weight_0']  # [B, action_dim]
            # 应用权重调整（这里简化处理）
            # 计算每个样本的权重调整
            if weight_adjustment.dim() == 2:
                # [B, action_dim] -> [B, 1, 1] 用于广播
                weight_factor = 1.0 + 0.1 * weight_adjustment.mean(dim=1, keepdim=True).unsqueeze(-1)
            else:
                # [B] -> [B, 1, 1]
                weight_factor = 1.0 + 0.1 * weight_adjustment.unsqueeze(-1).unsqueeze(-1)
            scores = scores * weight_factor

        # 选择top-k特征
        _, selected_indices = torch.topk(scores, topk, dim=-1)  # [B, N, topk]
        selected = torch.gather(
            candidate_feats,  # [B, N, num_candidates, D]
            2,  # 在num_candidates维度上gather
            selected_indices.unsqueeze(-1).expand(-1, -1, -1, D)  # [B, N, topk, D]
        )

        return torch.mean(selected, dim=2)  # [B, N, D]

class AttentionWithCoT(nn.Module):
    """带CoT的注意力层 - 支持多代理PPO"""
    def __init__(self, feat_dim, num_heads):
        super().__init__()
        self.feat_dim = feat_dim
        self.num_heads = num_heads
        self.head_dim = feat_dim // num_heads
        self.update_gate = nn.Sequential(
            nn.Linear(2 * feat_dim, feat_dim),
            nn.Sigmoid()
        )
        # 投影层
        self.q_proj = nn.Linear(feat_dim, feat_dim)
        self.k_proj = nn.Linear(feat_dim, feat_dim)
        self.v_proj = nn.Linear(feat_dim, feat_dim)
        self.out_proj = nn.Linear(feat_dim, feat_dim)

        # 多代理PPO相关
        self.ppo_enabled = False
        self.attention_agent = None

    def forward(self, q, k, v, q_hid):
        """输入维度均为 [B, S, D]"""
        B, S, D = q.shape

        # 确保所有输入都是float32类型
        q = q.float()
        k = k.float()
        v = v.float()
        q_hid = q_hid.float()

        # 线性投影
        Q = self.q_proj(q).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_proj(k).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)
        #K = k.view(B, S, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_proj(v).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)

        # 注意力计算
        attn_weights = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        attn_weights = F.softmax(attn_weights, dim=-1)

        # 加权聚合
        output = torch.matmul(attn_weights, V).transpose(1, 2).contiguous()
        output = output.view(B, S, D)
        #return self.out_proj(output)


        attn_output = self.out_proj(output)
        combined = torch.cat([q_hid, attn_output], dim=-1)
        update = self.update_gate(combined)
        h_new = update * q_hid + (1 - update) * attn_output

        return h_new

    def enable_attention_ppo(self, state_dim: int, action_dims: dict, hidden_dim: int = 256):
        """启用注意力层的PPO训练"""
        self.ppo_enabled = True
        from multi_agent_ppo import AttentionAgent, AgentConfig

        config = AgentConfig(
            agent_id=f"attention_{id(self)}",
            state_dim=state_dim,
            action_dims=action_dims,
            hidden_dim=hidden_dim
        )

        self.attention_agent = AttentionAgent(config)

        # 确保代理在正确的设备上
        device = next(self.q_proj.parameters()).device
        self.attention_agent = self.attention_agent.to(device)

    def prepare_attention_state(self, q: torch.Tensor, k: torch.Tensor, v: torch.Tensor) -> torch.Tensor:
        """准备注意力状态表示"""
        B, S, D = q.shape

        # 计算查询、键、值的统计信息
        q_mean = torch.mean(q, dim=1)  # [B, D]
        k_mean = torch.mean(k, dim=1)  # [B, D]
        v_mean = torch.mean(v, dim=1)  # [B, D]

        # 计算注意力相关性
        qk_similarity = F.cosine_similarity(q_mean, k_mean, dim=-1)  # [B]
        qk_similarity = qk_similarity.unsqueeze(-1)  # [B, 1] 手动添加维度

        # 组合状态
        state = torch.cat([q_mean, k_mean, v_mean, qk_similarity], dim=-1)  # [B, 3*D + 1]

        return state

    def forward_ppo(self, q: torch.Tensor, k: torch.Tensor, v: torch.Tensor, q_hid: torch.Tensor, deterministic: bool = False):
        """PPO模式的前向传播"""
        if not self.ppo_enabled or self.attention_agent is None:
            return self.forward(q, k, v, q_hid), None

        # 准备状态
        state = self.prepare_attention_state(q, k, v)

        # 获取注意力代理的动作
        actions, log_probs, entropy, value = self.attention_agent.get_action_and_value(state, deterministic)

        # 应用注意力动作
        output = self.apply_attention_actions(q, k, v, q_hid, actions)

        # 准备PPO数据
        ppo_data = {
            'state': state,
            'actions': actions,
            'log_probs': log_probs,
            'value': value,
            'entropy': entropy
        }

        return output, ppo_data

    def apply_attention_actions(self, q: torch.Tensor, k: torch.Tensor, v: torch.Tensor, q_hid: torch.Tensor, actions: dict) -> torch.Tensor:
        """应用注意力动作"""
        B, S, D = q.shape

        # 确保所有输入都是float32类型
        q = q.float()
        k = k.float()
        v = v.float()
        q_hid = q_hid.float()

        # 线性投影
        Q = self.q_proj(q).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_proj(k).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_proj(v).view(B, S, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力权重
        attn_weights = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)

        # 应用PPO学习的注意力权重调整
        if 'attention_weights' in actions:
            # 获取注意力权重动作 [B]
            attention_action = actions['attention_weights']  # [B]

            # 创建注意力掩码
            attention_mask = torch.zeros_like(attn_weights)  # [B, num_heads, S, S]

            for b in range(B):
                # 根据动作选择注意力模式
                action_idx = attention_action[b].item()
                if action_idx < S:
                    # 聚焦于特定位置
                    attention_mask[b, :, :, action_idx] = 1.0
                else:
                    # 均匀注意力
                    attention_mask[b, :, :, :] = 1.0 / S

            # 应用注意力掩码
            attn_weights = attn_weights + 0.1 * attention_mask

        attn_weights = F.softmax(attn_weights, dim=-1)

        # 加权聚合
        output = torch.matmul(attn_weights, V).transpose(1, 2).contiguous()
        output = output.view(B, S, D)

        # 应用输出投影
        attn_output = self.out_proj(output)

        # CoT更新门
        combined = torch.cat([q_hid, attn_output], dim=-1)
        update = self.update_gate(combined)
        h_new = update * q_hid + (1 - update) * attn_output

        return h_new


class TextFeatureProcessor(nn.Module):
    def __init__(self, input_dim, output_dim):
        super(TextFeatureProcessor, self).__init__()
        self.layers = nn.Sequential(

            nn.Linear(input_dim, 4096),
            nn.ReLU(),
            #nn.Dropout(p=0.5),
            #nn.Linear(4096, 2048),
            #nn.ReLU(),
            nn.Linear(4096, output_dim)
        )

    def forward(self, text_features):
        return self.layers(text_features)

# ==================== 完整模型 ====================
class HierarchicalAttentionModel(nn.Module):
    def __init__(self, num_classes=10, num_layers=4, feat_dim=512):
        super().__init__()
        self.feat_dim = feat_dim
        self.num_layers = num_layers
        self.text_processor = TextFeatureProcessor(8192, 512)
        # 特征编码器
        self.encoder = LongCLIPEncoder(feat_dim)
        self.LearnablePruning=LearnablePruning(512)
        # 动态选择器
        self.selectors = nn.ModuleList([
            DynamicSelector(feat_dim, initial_topk=25) for _ in range(num_layers)
        ])

        # 注意力层
        self.attention_layers = nn.ModuleList([
            AttentionWithCoT(feat_dim, num_heads=8) for _ in range(num_layers)
        ])

        '''
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(feat_dim, 256),
            nn.ReLU(),
            nn.Linear(256, num_classes))
        '''

        self.classifier = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, num_classes))
        # 记忆库
        self.memory_bank = FaissMemoryBank(feat_dim=512, capacity=100000, use_gpu=True)
        self.norm = nn.LayerNorm(feat_dim)
    '''
    def init_memory_bank(self, unlabeled_loader):
        """预计算无标签特征"""
        self.eval()
        with torch.no_grad():
            for images in tqdm(unlabeled_loader, desc="Building Memory Bank"):
                feats = self.encoder.encode_image(images)
                self.memory_bank.add(feats.cpu())
        self.train()
    '''
    def init_memory_bank(self, unlabeled_loader):
        """预计算无标签特征"""
        self.eval()
        with torch.no_grad():
            for images,texts in tqdm(unlabeled_loader, desc="Building Memory Bank"):
                #print(len(texts[0][0]))
                #print(len(texts[0][1]))
                #feats_image = self.encoder.encode_image(images)
                feats_text_0 = self.encoder.encode_text(texts[0])
                feats_text_1 = self.encoder.encode_text(texts[1])
                feats_text_2 = self.encoder.encode_text(texts[2])
                feats_text_3 = self.encoder.encode_text(texts[3])
                feats_text_0 = feats_text_0[:, :197, :]
                feats_text_1 = feats_text_1[:, :197, :]
                feats_text_2 = feats_text_2[:, :197, :]
                feats_text_3 = feats_text_3[:, :197, :]
                #feats=feats_image+feats_text
                self.memory_bank.add([feats_text_0.cpu(),feats_text_1.cpu(),feats_text_2.cpu(),feats_text_3.cpu()])
            self.memory_bank.shareindex()
        self.train()


    def forward(self, x,input_image,data_gpus, key_to_gpu,id2filename,index,current_epoch=0):
        """
        输入:
            x: 字典 {'image': [B,C,H,W], 'text': [B,L]}
        输出:
            logits: [B, num_classes]
        """
        # 初始特征提取
        img_feat = self.encoder.encode_image(x['image'])  # [B, D]
        #text_feat = self.encoder.encode_text(x['text'])  # [B, D]
        #text_feat= text_feat[:, :197, :]
        h = img_feat  # [32,197,512]
        #print(h.shape)
        # 分层处理
        for layer_idx in range(self.num_layers):
            retrieval_stage = layer_idx % 4
            '''
            # 动态选择无标签特征 [B, topk, D]
            selected = self.selectors[layer_idx](
                h,  # 当前层特征均值
                self.memory_bank,
                current_epoch
            )
            #print(selected.shape)
            # selected.shape = [32, 5, 8, 64]
            #selected = selected.mean(dim=1)  # 结果形状 [32, 8, 64]
            # 注意力增强
            h = self.attention_layers[layer_idx](
                q=selected,  # 无标签特征作为Q
                k=h,        # 有标签特征作为K
                v=h          # 有标签特征作为V
            )
            '''
            # 改为只在第一层使用记忆库
            if layer_idx < 4:  # 只在特定层使用记忆库
                selected = self.selectors[layer_idx](
                    h,
                    self.memory_bank,
                    current_epoch,
                    retrieval_stage  # 传入当前检索阶段
                )
                if layer_idx==0:
                    q_hid = img_feat
                else:
                    q_hid = selected
                h = self.attention_layers[layer_idx](q=selected, k=h, v=h, q_hid=q_hid)
                h = self.norm(h)
            else:
                h = self.attention_layers[layer_idx](q=h, k=h, v=h,q_hid=q_hid)
                h = self.norm(h)


            # 残差连接
            if layer_idx > 0:
                h = h + h_prev
            h_prev = h

        # 分类
        h_pooled = torch.mean(h, dim=1)  # 或使用max pooling
        print("h_pooled的形状:",h_pooled.shape)

        #图像字幕方法
        T= image_search(input_image, id2filename, data_gpus, key_to_gpu,index, k=11).to(device)
        #T2,top_matches = batch_search(input_image,kmeans, image_bow_representations,data_gpus, key_to_gpu,image_paths,idf,k1=1.5, b=0.75, avgdl=50,is_path=False)
        #T = torch.cat((T1, T2), dim=1).to(device)
        #print(M.shape)
        #print(T.shape)
        f1 = self.text_processor(T)
        f1=torch.mean(f1, dim=1)
        print("f1的形状:",f1.shape)
        result=torch.cat((h_pooled, f1), dim=1)
        logits = self.classifier(result)

        #logits = self.classifier(h[:, 0, :])
        return logits

    def forward_ppo(self, x, input_image, data_gpus, key_to_gpu, current_epoch=0):
        """
        PPO模式的前向传播
        输入:
            x: 字典 {'image': [B,C,H,W], 'text': [B,L]}
        输出:
            logits: [B, num_classes]
            ppo_data_list: PPO相关数据列表
        """
        # 初始特征提取
        img_feat = self.encoder.encode_image(x['image'])  # [B, D]
        h = img_feat  # [32,197,512]

        ppo_data_list = []  # 存储每层的PPO数据

        # 分层处理
        for layer_idx in range(self.num_layers):
            retrieval_stage = layer_idx % 4

            # 改为只在第一层使用记忆库
            if layer_idx < 4:  # 只在特定层使用记忆库
                # 检查选择器是否启用了PPO
                if hasattr(self.selectors[layer_idx], 'ppo_enabled') and self.selectors[layer_idx].ppo_enabled:
                    # PPO模式下调用选择器
                    selected, ppo_data = self.selectors[layer_idx].forward_ppo(
                        h,
                        self.memory_bank,
                        current_epoch,
                        retrieval_stage
                    )
                    ppo_data_list.append(ppo_data)
                else:
                    # 普通模式
                    selected = self.selectors[layer_idx](
                        h,
                        self.memory_bank,
                        current_epoch,
                        retrieval_stage
                    )
                    ppo_data_list.append(None)

                if layer_idx==0:
                    q_hid = img_feat
                else:
                    q_hid = selected
                h = self.attention_layers[layer_idx](q=selected, k=h, v=h, q_hid=q_hid)
                h = self.norm(h)
            else:
                h = self.attention_layers[layer_idx](q=h, k=h, v=h, q_hid=q_hid)
                h = self.norm(h)
                ppo_data_list.append(None)  # 非PPO层添加None

            # 残差连接
            if layer_idx > 0:
                h = h + h_prev
            h_prev = h

        # 分类
        h_pooled = torch.mean(h, dim=1)  # 或使用max pooling

        #图像字幕方法 - 使用全局变量
        try:
            # 尝试使用全局变量
            T = image_search(input_image, id2filename, data_gpus, key_to_gpu, index, k=11).to(device)
            f1 = self.text_processor(T)
            f1 = torch.mean(f1, dim=1)
            result = torch.cat((h_pooled, f1), dim=1)
        except NameError:
            # 如果全局变量不存在，使用零填充
            print("警告: PPO模式下缺少全局变量，使用零填充")
            f1 = torch.zeros(h_pooled.size(0), 512, device=h_pooled.device)
            result = torch.cat((h_pooled, f1), dim=1)

        logits = self.classifier(result)

        return logits, ppo_data_list

# ==================== 对抗训练 ====================
class Discriminator(nn.Module):
    """特征判别器"""
    def __init__(self, feat_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(feat_dim, 256),
            nn.LeakyReLU(0.2),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return self.net(x)

def adversarial_loss(real_feat, fake_feat, discriminator):
    real_loss = torch.log(discriminator(real_feat) + 1e-8).mean()
    fake_loss = torch.log(1 - discriminator(fake_feat) + 1e-8).mean()
    return -(real_loss + fake_loss)  # 最大化判别难度

def setup_logger(log_file="training.log"):
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)  # 设定日志级别

    # 如果已经有handler，先移除，避免重复日志
    if logger.hasHandlers():
        logger.handlers.clear()

    # 创建handler（输出到文件）
    fh = logging.FileHandler(log_file, mode='w')
    fh.setLevel(logging.INFO)

    # 创建handler（输出到控制台，可选）
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)

    # 创建formatter
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s',
                                  datefmt='%m-%d %H:%M:%S')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)

    # 添加handler到logger
    logger.addHandler(fh)
    logger.addHandler(ch)

    return logger
# ==================== 训练流程 ====================
def train_model():
    # 初始化
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = HierarchicalAttentionModel(num_classes=10).to(device)
    discriminator = Discriminator(model.feat_dim).to(device)
    logger = setup_logger("train_cot_unlabel_rat_Dantopk_imagetext_(4)(6)_4_pool_select1_LayerNorm_classlayer1_gpt_npk1_implicit_concat_512_rnn_setting2.log")

    trainset = share4v_train_dataset()
    train_loader = torch.utils.data.DataLoader(trainset, batch_size=32, shuffle=True)


    testset = share4v_val_dataset()
    eval_trainloader = DataLoader(testset, batch_size=128, shuffle=False)

    # 测试数据加载器
    testloader = DataLoader(testset, batch_size=32, shuffle=False)


    unlabel_trainset = share4v_unlabel_dataset()
    unlabeled_loader = DataLoader(unlabel_trainset, batch_size=512, shuffle=False)
    #print(f"一共跳过了 {unlabel_trainset.skip_count} 次")
    #print(unlabel_trainset[49999])
    # 初始化记忆库
    model.init_memory_bank(unlabeled_loader)

    # 优化器
    main_params = list(model.parameters())
    dis_params = list(discriminator.parameters())
    optimizer = torch.optim.Adam(main_params, lr=1e-4)
    dis_optimizer = torch.optim.Adam(dis_params, lr=1e-4)
    best_accuracy = 0
    torch.cuda.empty_cache()  # 强制释放未使用的缓存
    # 训练循环
    for epoch in range(500):
        model.train()
        total_loss = 0
        total_correct = 0
        total_samples = 0
        for batch_idx, (images,caption, labels,image_cliptensor) in tqdm(enumerate(train_loader)):
            labels = labels.to(device)
            image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].squeeze(1)
            # 前向传播
            logits = model({
                'image': images,
                'text': caption  # 模拟文本输入
            },image_cliptensor,data_gpus, key_to_gpu, current_epoch=epoch)

            # 分类损失
            cls_loss = F.cross_entropy(logits, labels)

            # 对抗训练（每5步更新一次判别器）
            #if batch_idx % 5 == 0:
                #with torch.no_grad():
                    #real_feat = model.encoder.encode_image(images)
                    #fake_feat = model.memory_bank.sample(real_feat).to(device)

                #dis_loss = adversarial_loss(real_feat, fake_feat, discriminator)
                #loss = cls_loss + 0.3 * dis_loss
                #optimizer.zero_grad()
                #dis_optimizer.zero_grad()
                #loss.backward()
                #optimizer.step()
                #dis_optimizer.step()
            #else:
                # 普通批次只计算分类损失
            optimizer.zero_grad()
            cls_loss.backward()
            optimizer.step()
            # 课程学习调整

            if epoch > 10:
                new_topk = max(1, int(25 * (0.9 ** (epoch-10))))
                for selector in model.selectors:
                    selector.update_topk(new_topk)

            # 总损失
            #loss = cls_loss + 0.3 * dis_loss if batch_idx % 5 == 0 else cls_loss
            loss = cls_loss
            total_loss += loss.item()
            _, predicted = torch.max(logits.data, 1)
            total_correct += (predicted == labels).sum().item()
            total_samples += labels.size(0)
        eval_accuracy=evaluate(model, eval_trainloader, device)
        train_accuracy = total_correct / total_samples
        print(f"Epoch {epoch + 1}, Loss: {total_loss/len(train_loader):.4f}, Train Accuracy: {train_accuracy * 100}%,eval Accuracy:{eval_accuracy*100}%")
        logger.info(f"Epoch {epoch + 1}: loss={total_loss/len(train_loader):.4f}, accuracy={train_accuracy * 100}%,eval Accuracy:{eval_accuracy*100}%")
        if eval_accuracy > best_accuracy:
        #if average_loss < best_loss:
            #best_loss = average_loss
            best_accuracy = eval_accuracy
            torch.save(model.state_dict(), 'train_cot_unlabel_rat_Dantopk_imagetext_(4)(6)_4_pool_select1_LayerNorm_classlayer1_gpt_nok1_implicit_concat_512_rnn_setting2.pth')
            print("Saved new best model weights.")
            #compare_weights('previous_best_model_weights.pth', 'best_model_weights.pth')
def evaluate(model, dataloader, device):
    """
    评估模型在验证集上的准确率

    参数:
        model: 要评估的模型
        dataloader: 验证集数据加载器
        device: 计算设备 (cuda/cpu)

    返回:
        accuracy: 模型在验证集上的准确率
    """
    model.eval()  # 设置为评估模式
    total_correct = 0
    total_samples = 0

    with torch.no_grad():  # 禁用梯度计算
        for images, captions, labels,image_cliptensor in tqdm(dataloader, desc="Evaluating"):
            images = images.to(device)
            labels = labels.to(device)
            image_cliptensor['pixel_values'] = image_cliptensor['pixel_values'].squeeze(1)
            # 前向传播
            logits = model({
                'image': images,
                'text': captions
            },image_cliptensor,data_gpus, key_to_gpu, current_epoch=0)  # 评估时不使用课程学习

            # 计算预测结果
            _, predicted = torch.max(logits.data, 1)
            total_samples += labels.size(0)
            total_correct += (predicted == labels).sum().item()

    accuracy = total_correct / total_samples
    return accuracy

def build_faiss_index_and_shard(index_cpu, d=512, gpu_ids=[0,1,2]):
    """
    传入numpy的特征 [N, d], 在CPU上建IndexFlatL2,
    然后让Faiss自动分Shard到多张GPU.
    """
    # Step2: shard到多GPU
    co = faiss.GpuMultipleClonerOptions()
    co.shard = True  # 表示分块
    gpu_resources = []
    for i in gpu_ids:
        res = faiss.StandardGpuResources()
        gpu_resources.append(res)

    # 生成多GPU索引
    gpu_index = faiss.index_cpu_to_all_gpus(
        index_cpu,
        co=co
    )
    print("Multi-GPU sharded index built.")
    return gpu_index


if __name__ == "__main__":
    #setting2
    with open('/data1/wangj/RAFT/id2filename_setting3_weight.json', 'r') as file:
        id2filename = json.load(file)
    index_cpu = faiss.read_index("/data1/wangj/RAFT/index_file_setting3_weight.faiss")
    index=build_faiss_index_and_shard(index_cpu)
    del index_cpu
    gc.collect()

    '''
    #setting2
    print("this step")
    image_paths=[]
    directory = "/data1/wangj/RAFT/unlabel_setting3"
    for i in range(len(os.listdir(directory))):
        image_paths.append(f"/data1/wangj/RAFT/unlabel_setting3/image_{i}.png")
    #with open('/data1/wangj/RAFT/all_patch_features_256.pkl', 'rb') as f:
        #all_patch_features = pickle.load(f)
    print("all_patch_features is already")
    with open('/data1/wangj/RAFT/kmeans_model_256_setting3.pkl', 'rb') as f:
        kmeans = pickle.load(f)
    print("kmeans is already")
    #with open('/data1/wangj/RAFT/visual_words_256.pkl', 'rb') as f:
        #visual_words = pickle.load(f)
    print("visual_words is already")
    print("MiniBatchKMeans clustering complete. Visual words generated:", kmeans.cluster_centers_.shape)
    with open('/data1/wangj/RAFT/image_bow_representations_256_setting3.pkl', 'rb') as f:
        image_bow_representations = pickle.load(f)
    # 计算IDF，假设有N个图像文档
    N = len(os.listdir(directory))
    num_clusters = kmeans.n_clusters
    # 初始化 IDF 字典
    idf = {}
    for word_idx in range(num_clusters):
        # 计算有多少文档包含该视觉词汇
        n_t = sum([1 for bow in image_bow_representations if bow[word_idx] > 0])

        # 计算 IDF： log((N - n_t + 0.5) / (n_t + 0.5))，确保平滑
        idf[word_idx] = np.log((N - n_t + 0.5) / (n_t + 0.5))
        # BM25参数

    k1 = 1.5
    b = 0.75
    avgdl = 50  # 平均文档长度
    '''
    train_model()
