# RL_cot_train.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from peft import LoraConfig, get_peft_model
from tqdm import tqdm
import faiss
import sys
sys.path.append("/home/<USER>/RAFT/Long-CLIP-main")
from train_longclip.sharegpt4v import (
    share4v_train_dataset,
    share4v_val_dataset,
    share4v_unlabel_dataset
)
from ppo_training import ppo_training_loop
from ppo_buffer import logger,safe_assert
# 设备设置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


# --- PPO Agent 定义 ---
class PPOAgent(nn.Module):
    def __init__(self, state_dim, action_dims, hidden_dim=256, continuous_action_ranges=None):
        super().__init__()
        self.action_dims = action_dims
        self.continuous_action_ranges = continuous_action_ranges

        self.actor_base = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
        )
        self.actor_heads = nn.ModuleDict()
        for name, dim in action_dims.items():
            self.actor_heads[name] = nn.Linear(hidden_dim, dim)
        self.critic = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        # 更稳定的初始化
        nn.init.zeros_(self.critic[2].bias)
        nn.init.xavier_uniform_(self.critic[2].weight, gain=0.1)  # 减小初始化范围
        nn.init.xavier_uniform_(self.critic[0].weight, gain=1.0)
        nn.init.zeros_(self.critic[0].bias)

        # 可学习的 log_std（连续动作）- 初始化为较小的负值
        if any("continuous" in k for k in action_dims):
            self.log_std = nn.ParameterDict({
                k: nn.Parameter(torch.full((dim,), -1.0))  # 初始化为-1而不是0，对应std=0.37
                for k, dim in action_dims.items() if "continuous" in k
            })


    def get_action_and_value(self, state, deterministic=False):
        assert not torch.isnan(state).any(), f"NaN in input state, shape={state.shape}"
        base = self.actor_base(state)                  # [6304, hidden]
        assert not torch.isnan(base).any(), f"NaN in actor_base output, state_shape={state.shape}"
        log_probs, actions, entropies = {}, {}, []

        for name, head in self.actor_heads.items():
            params = head(base)
            assert not torch.isnan(params).any(), f"NaN in actor_head {name}, params_shape={params.shape}"
            if "discrete" in name:
                dist = torch.distributions.Categorical(logits=params)
                act  = dist.probs.argmax(-1) if deterministic else dist.sample()
                cur_ent = dist.entropy().view(state.size(0), -1).sum(-1)   # [6304]
                log_probs[name] = dist.log_prob(act)
            else:  # continuous
                mean = params
                if "continuous_rerank" in name:
                    mean = torch.sigmoid(mean)
                elif "continuous_attn_mod" in name:
                    mean = torch.clamp(mean, -2.0, 2.0)
                std = torch.exp(torch.clamp(self.log_std[name], -5, 1))  # 减小范围防止数值爆炸
                assert not torch.isnan(mean).any(), f"NaN in mean for {name}"
                assert not torch.isnan(std).any(), f"NaN in std for {name}"
                dist = torch.distributions.Normal(mean, std)
                act  = mean if deterministic else dist.sample()
                cur_log_prob = dist.log_prob(act).sum(-1)
                assert not torch.isnan(cur_log_prob).any(), f"NaN in log_prob for {name}"
                log_probs[name] = cur_log_prob
                # 随后 squash/rescale …
                cur_ent = dist.entropy().view(state.size(0), -1).sum(-1)    # [6304]

            actions[name] = act
            entropies.append(cur_ent)

        total_log_prob = torch.stack(list(log_probs.values()), 0).sum(0)    # [6304]
        total_entropy  = torch.stack(entropies, 0).sum(0)                   # [6304]
        value          = self.critic(state)                                 # [6304,1]
        safe_assert('PPOAgent value', value)
        safe_assert('PPOAgent total_log_prob', total_log_prob)
        safe_assert('PPOAgent total_entropy', total_entropy)
        return actions, total_log_prob, total_entropy, value


# --- LongCLIP 编码器，加载 SFT 权重 ---
class LongCLIPEncoder(nn.Module):
    def __init__(self, feat_dim=512, sft_weights_path=None):
        super().__init__()
        # 加载 Long-CLIP 模型
        import sys
        sys.path.append("/home/<USER>/RAFT/Long-CLIP-main")
        from model import longclip
        self.model, _ = longclip.load(
            "/home/<USER>/RAFT/Long-CLIP-main/checkpoints/LongCLIP-B/longclip-B.pt",
            device='cpu'
        )
        # 应用 LoRA
        config = LoraConfig(
            r=8, lora_alpha=8,
            target_modules=["resblocks.11.attn.out_proj"],
            task_type="SEQ_CLS", lora_dropout=0.05,
            bias="none", inference_mode=False,
            use_rslora=True, init_lora_weights="gaussian"
        )
        self.model = get_peft_model(self.model, config)

        # 注意：SFT权重将在HierarchicalAttentionModel中统一加载
        # 这里不再单独加载，避免重复
        self.model.to(device)

    def encode_image(self, imgs):
        imgs = imgs.to(device)
        with torch.no_grad():
            feat = self.model.encode_image(imgs)
            feat = feat / feat.norm(dim=-1, keepdim=True)
        return feat

    def encode_text(self, texts):
        from model import longclip
        tokenized = longclip.tokenize(texts, truncate=True).to(device)
        with torch.no_grad():
            feat = self.model.encode_text_full(tokenized)
            feat = feat / feat.norm(dim=-1, keepdim=True)
        return feat

# --- 可学习剪枝（保持不变） ---
class LearnablePruning(nn.Module):
    def __init__(self, feature_dim, num_keep_tokens=197):
        super().__init__()
        self.num_keep_tokens = num_keep_tokens
        self.scoring = nn.Sequential(
            nn.Linear(feature_dim, feature_dim//2),
            nn.ReLU(),
            nn.Linear(feature_dim//2, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        scores = self.scoring(x).squeeze(-1)
        idx = scores.topk(self.num_keep_tokens, dim=-1).indices
        return torch.gather(
            x, 1, idx.unsqueeze(-1).expand(-1,-1,x.size(-1))
        )

# --- Faiss 记忆库 ---
class FaissMemoryBank:
    def __init__(self, feat_dim=512, capacity=50000, use_gpu=False):
        self.feat_dim = feat_dim
        self.capacity = capacity
        self.cpu_indexes = [None, None, None, None]  # 三个独立的索引
        self.gpu1_indexes = [None, None, None, None]
        self.gpu2_indexes = [None, None, None, None]
        self.gpu3_indexes = [None, None, None, None]
        self.shards_index= [None, None, None, None]
        self.features_list = [[], [], [], []]  # 存储三个特征组的列表
        self.use_gpu = use_gpu
        
        # 初始化Faiss索引
        for i in range(4):
            self.reset_index(i)
        
    def reset_index(self, idx):
        """初始化或重置指定索引"""
        if self.cpu_indexes[idx] is not None:
            del self.cpu_indexes[idx]
        if self.gpu1_indexes[idx] is not None:
            del self.gpu1_indexes[idx]
        if self.gpu2_indexes[idx] is not None:
            del self.gpu2_indexes[idx]
        if self.gpu3_indexes[idx] is not None:
            del self.gpu3_indexes[idx]
            
        self.cpu_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu1_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu2_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        self.gpu3_indexes[idx] = faiss.IndexFlatL2(self.feat_dim)
        if self.use_gpu:
            res1 = faiss.StandardGpuResources()
            res2 = faiss.StandardGpuResources()
            res3 = faiss.StandardGpuResources()
            self.gpu1_indexes[idx] = faiss.index_cpu_to_gpu(res1, 1, self.gpu1_indexes[idx])
            self.gpu2_indexes[idx] = faiss.index_cpu_to_gpu(res2, 2, self.gpu2_indexes[idx])
            self.gpu3_indexes[idx] = faiss.index_cpu_to_gpu(res3, 3, self.gpu3_indexes[idx])
        
        self.features_list[idx] = np.zeros((0, self.feat_dim), dtype='float32')

    def add(self, features_list):
        """添加三组特征到记忆库"""
        for i in range(4):
            features = features_list[i]
            #print(f"Adding features to index {i}: {features.shape}")
            if isinstance(features, torch.Tensor):
                features = features.cpu().numpy().astype('float32')
            
            # 检查容量
            #if len(self.features_list[i]) + len(features) > self.capacity:
            #    self._remove_oldest(i, self.capacity - len(features))
            try:    
                self.gpu1_indexes[i].add(features.reshape(-1, self.feat_dim))
            except RuntimeError as e:
                # 假设这里捕获“显存不足”的错误
                print("GPU1 内存不足，退回到 GPU2 索引")
                try:
                    self.gpu2_indexes[i].add(features.reshape(-1, self.feat_dim))
                except RuntimeError as e:
                    print("GPU2 内存不足，退回到 GPU3 索引")
                    try:
                        self.gpu3_indexes[i].add(features.reshape(-1, self.feat_dim))
                    except RuntimeError as e:
                        print("GPU3 内存不足，退回到 CPU 索引")
                        self.cpu_indexes[i].add(features.reshape(-1, self.feat_dim))
            self.features_list[i] = np.vstack([self.features_list[i], features.reshape(-1, self.feat_dim) ])
            print(f"添加特征到索引 {i}: {features.shape}")
            print(f"当前特征列表大小: {self.features_list[i].shape}")
    def _remove_oldest(self, idx, num_to_keep):
        """FIFO淘汰旧特征"""
        self.features_list[idx] = self.features_list[idx][-num_to_keep:]
        self.reset_index(idx)
        self.indexes[idx].add(self.features_list[idx])

    def shareindex(self):
        for i in range(4):
            self.shards_index[i] = faiss.IndexShards(self.feat_dim)
                # 把 GPU 索引（已经在显存中）和 CPU 索引都加进去
            self.shards_index[i].addIndex(self.gpu1_indexes[i])  
            #self.shards_index[i].addIndex(self.gpu2_indexes[i])
            #self.shards_index[i].addIndex(self.gpu3_indexes[i])
            #self.shards_index[i].addIndex(self.cpu_indexes[i])


    def search(self, queries, k=5,stage=0):
        """
        搜索最相似的k个特征
        输入: queries [B, D]
        返回: 
            distances: [B, k]
            indices: [B, k]
        """
        #print(f"Faiss 索引中的向量数量: {self.shards_index[stage].at(0).ntotal}")
        if isinstance(queries, torch.Tensor):
            queries = queries.detach().cpu().numpy().reshape(-1, self.feat_dim).astype('float32')
            
        distances, indices = self.shards_index[stage].search(queries.reshape(-1, self.feat_dim), k)
        #print(indices)
        return distances, indices


# --- DynamicSelector（PPO 版本） ---
class DynamicSelector(nn.Module): #如果增加一部分随机索引的作为噪声呢
    def __init__(self, feat_dim, ppo_agent: PPOAgent, initial_topk=25):
        super().__init__()
        self.feat_dim = feat_dim
        self.ppo_agent = ppo_agent
        self.initial_topk = initial_topk
        self.scorer = nn.Sequential(
            nn.Linear(feat_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

    def prepare_state(self, query, pool):#如何设置 #维度设置成[b,n,d]还是[b,d]
        # 拼接 query & mean(pool)
        #print(f"Shape of query: {query.shape}")
        #print(f"Shape of pool: {pool.shape}")
        if pool.dim()==2: pool = pool.unsqueeze(0)
        #query = torch.mean(query, dim=1)
        #mean_pool = torch.mean(pool,dim=(1, 2))
        mean_pool = torch.mean(pool,dim=1)
        #print(f"Shape of mean_pool: {mean_pool.shape}")
        return torch.cat([query, mean_pool], dim=-1)

    def forward(self, query, memory_bank, ppo_mode=False, deterministic_ppo=False,retrieval_stage=0):
        device = query.device
        #print(f"Shape of image_feature: {query.shape}")
        B, N, D = query.shape
        _, idxs = memory_bank.search(query, k=self.initial_topk,stage=retrieval_stage)
        # 收集候选特征
        candidate_feats = torch.stack([
            torch.from_numpy(memory_bank.features_list[retrieval_stage][indices]).to(device)
            for indices in idxs
        ])
        #print(f"Shape of candidate_feats: {candidate_feats.shape}")
        if ppo_mode:
            state = self.prepare_state(query.reshape(-1, self.feat_dim), candidate_feats)
            #print(f"Shape of state: {state.shape}")
            acts, lp, ent, val = self.ppo_agent.get_action_and_value(state, deterministic_ppo)
            #print(acts["discrete_top_k"].shape)
            # 这里略去详细 action->selected_features 转换，用 placeholder
            k = acts["discrete_top_k"].long() + 1
                    # ② 连续评分  --------------------------
            scores = acts["continuous_rerank"]        # [Q, 25]
            # 取每条 query 自己的 top-k
            top_idx = torch.topk(scores, k.max().item(), dim=1).indices  # [Q, k_max]

            # 用 gather 选 token，最后裁到各自 k
            batch_idx = torch.arange(candidate_feats.size(0), device=device).unsqueeze(-1)
            sel = candidate_feats[batch_idx, top_idx]        # [Q, k_max, 512]

            # 按样本实际 k 截断并打包到列表，再 pad/stack
            sel_list = [ sel[i, :k[i], :] for i in range(sel.size(0)) ]                      
            sel_feat_pooled = torch.stack([
                    sel_list[i].mean(0) for i in range(len(sel_list))
                    ], dim=0)  
            sel_feat_pooled=sel_feat_pooled.view(B, N, D)
            #print(f"Shape of sel_feat_pooled: {sel_feat_pooled.shape}")
            #print(f"Shape of acts: {acts['continuous_rerank'].shape}")
            #print(f"Shape of lp: {lp.shape}")
            #print(f"Shape of value: {val.shape}")
            #print(f"Shape of selected_features: {sel_feat_pooled.shape}")
            #print(f"Shape of query: {query.shape}")
            #print(f"Shape of state: {state.shape}")
            return sel_feat_pooled, {"actions":acts, "log_prob":lp, "value":val, "selected_features":sel_feat_pooled,"query":query,"state":state}
        else:
            pool = candidate_feats.view(B, N, self.initial_topk, D)
            scores = self.scorer(pool.float()).squeeze(-1)
            topk = scores.topk(self.initial_topk//2, -1).indices
            sel = torch.gather(pool.float(), 2, topk.unsqueeze(-1).expand(-1, -1, -1, D))
            return sel.mean(2), None

# --- AttentionWithCoT（PPO 版本） ---
class AttentionWithCoT(nn.Module):
    def __init__(self, feat_dim, num_heads, ppo_agent: PPOAgent):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim  = feat_dim // num_heads
        self.ppo_agent = ppo_agent
        self.q_proj = nn.Linear(feat_dim, feat_dim)
        self.k_proj = nn.Linear(feat_dim, feat_dim)
        self.v_proj = nn.Linear(feat_dim, feat_dim)
        self.out_proj = nn.Linear(feat_dim, feat_dim)
        self.update_gate = nn.Sequential(
            nn.Linear(2*feat_dim, feat_dim),
            nn.Sigmoid()
        )
    def prepare_state(self,q_hid, attn_raw):
        """
        q_hid   : [B, S, 512]
        attn_raw: [B, H, S, S]
        → state : [B*S, 520]   (512 + H=8)
        """
        B, S, D = q_hid.shape      # D=512
        H       = attn_raw.size(1) # 8

        attn_vec = attn_raw.mean(-1).permute(0, 2, 1)   # [B,S,H]
        state = torch.cat([
            q_hid.reshape(B*S, D),                      # [B*S,512]
            attn_vec.reshape(B*S, H)                    # [B*S,  8]
        ], dim=-1)                                      # [B*S,520]
        return state

    def forward(self, q, k, v, q_hid, ppo_mode=False, deterministic_ppo=False):
        #print(f"Shape of query: {q.shape}")
        B,S,D = q.shape
        Q = self.q_proj(q).view(B,S,self.num_heads,self.head_dim).transpose(1,2)
        K = self.k_proj(k).view(B,S,self.num_heads,self.head_dim).transpose(1,2)
        V = self.v_proj(v).view(B,S,self.num_heads,self.head_dim).transpose(1,2)
        attn_raw = (Q @ K.transpose(-2,-1)) / np.sqrt(self.head_dim)
        #print(f"Shape of q_hid: {q_hid.shape}")
        #print(f"Shape of attention raw: { attn_raw.mean((-1,-2)).shape}")
        if ppo_mode:
            state = self.prepare_state(q_hid, attn_raw)
            acts, lp, ent, val = self.ppo_agent.get_action_and_value(state, deterministic_ppo)
            mod_vec = acts["continuous_attn_mod"].view(B, S, D)             # [B,S,512]
            mod = mod_vec.view(B,S,self.num_heads,self.head_dim)            # [B,S,H,Dh]
            scale = torch.exp(mod.mean(-1)).permute(0,2,1).unsqueeze(-1)    # [B,H,S,1]
            attn_mod = attn_raw * scale                                     # [B,H,S,S]

            # 2-B 重新计算输出 ---------------------------------------------
            weights = F.softmax(attn_mod, -1)
            out = (weights @ V).transpose(1,2).reshape(B,S,D)

            # 2-C 用策略 gate ----------------------------------------------
            gate_raw = acts["continuous_gate"].view(B, S, D)
            gate = torch.sigmoid(gate_raw)
            h_new = gate * q_hid + (1 - gate) * self.out_proj(out)

            return h_new, {
                "actions": acts,
                "log_prob": lp,
                "value": val,
                "state": state          # 方便 reward 使用
            }
        else:
            weights = F.softmax(attn_raw, -1)
            out = (weights @ V).transpose(1,2).reshape(B,S,D)
            gate = self.update_gate(torch.cat([q_hid, out],-1))
            return gate*q_hid + (1-gate)*self.out_proj(out), None

# --- 文本特征处理器（保持不变） ---
class TextFeatureProcessor(nn.Module):
    def __init__(self, in_dim, out_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(in_dim, 4096), nn.ReLU(),
            nn.Linear(4096, out_dim)
        )
    def forward(self, x): return self.net(x)

# --- 完整模型：HierarchicalAttentionModel ---
class HierarchicalAttentionModel(nn.Module):
    def __init__(
        self,
        num_classes: int,
        num_layers: int,
        feat_dim: int,
        sft_weights_path: str,
        selector_ppo_agent_args: dict,
        attention_ppo_agent_args: dict
    ):
        super().__init__()
        self.num_layers = num_layers
        self.encoder = LongCLIPEncoder(feat_dim, sft_weights_path)
        self.text_processor = TextFeatureProcessor(8192, feat_dim)
        self.LearnablePruning = LearnablePruning(feat_dim)
        # 创建 PPOAgent 并附到 Selector/Attention
        self.selectors = nn.ModuleList()
        self.attention_layers = nn.ModuleList()
        for _ in range(num_layers):
            sel_agent = PPOAgent(**selector_ppo_agent_args)
            attn_agent = PPOAgent(**attention_ppo_agent_args)
            self.selectors.append(DynamicSelector(feat_dim, sel_agent))
            self.attention_layers.append(AttentionWithCoT(feat_dim, 8, attn_agent))
        self.classifier = nn.Sequential(
            nn.Linear(feat_dim*2, 512), nn.ReLU(),
            nn.Linear(512, num_classes)
        )
        self.memory_bank = FaissMemoryBank(feat_dim, use_gpu=True)
        self.norm = nn.LayerNorm(feat_dim)

        # 加载完整的SFT权重到整个模型
        if sft_weights_path:
            self.load_sft_weights(sft_weights_path)

    def load_sft_weights(self, sft_weights_path):
        """加载SFT权重到整个模型（不仅仅是LongCLIP）"""
        try:
            print(f"正在加载SFT权重: {sft_weights_path}")
            ckpt = torch.load(sft_weights_path, map_location='cpu')

            # 获取状态字典
            if 'model_state_dict' in ckpt:
                state_dict = ckpt['model_state_dict']
            else:
                state_dict = ckpt

            # 过滤出当前模型中存在的参数
            model_state_dict = self.state_dict()
            filtered_state_dict = {}

            for key, value in state_dict.items():
                if key in model_state_dict:
                    # 检查形状是否匹配
                    if model_state_dict[key].shape == value.shape:
                        filtered_state_dict[key] = value
                        print(f"✓ 加载参数: {key} {value.shape}")
                    else:
                        print(f"✗ 形状不匹配，跳过: {key} 期望{model_state_dict[key].shape} 实际{value.shape}")
                else:
                    print(f"✗ 参数不存在，跳过: {key}")

            # 加载过滤后的权重
            missing_keys, unexpected_keys = self.load_state_dict(filtered_state_dict, strict=False)

            print(f"成功加载 {len(filtered_state_dict)} 个参数")
            if missing_keys:
                print(f"缺失的参数: {len(missing_keys)} 个")
                for key in missing_keys[:5]:  # 只显示前5个
                    print(f"  - {key}")
            if unexpected_keys:
                print(f"意外的参数: {len(unexpected_keys)} 个")

        except Exception as e:
            print(f"加载SFT权重失败: {e}")
            print("将使用随机初始化的权重继续训练")

    def init_memory_bank(self, unlabeled_loader):
        """预计算无标签特征"""
        self.eval()
        with torch.no_grad():
            for images,texts in tqdm(unlabeled_loader, desc="Building Memory Bank"):
                #print(len(texts[0][0]))
                #print(len(texts[0][1]))
                #feats_image = self.encoder.encode_image(images)
                feats_text_0 = self.encoder.encode_text(texts[0])
                feats_text_1 = self.encoder.encode_text(texts[1])
                feats_text_2 = self.encoder.encode_text(texts[2])
                feats_text_3 = self.encoder.encode_text(texts[3])
                feats_text_0 = feats_text_0[:, :197, :]
                feats_text_1 = feats_text_1[:, :197, :]
                feats_text_2 = feats_text_2[:, :197, :]
                feats_text_3 = feats_text_3[:, :197, :]
                #feats=feats_image+feats_text  
                self.memory_bank.add([feats_text_0.cpu(),feats_text_1.cpu(),feats_text_2.cpu(),feats_text_3.cpu()])
            self.memory_bank.shareindex()
        self.train()

    def forward(self, images, texts, ppo_mode=False, deterministic_ppo=False):
        """
        返回 (logits, trajectory_data)：
          - ppo_mode=False: 返回 (logits, None)
          - ppo_mode=True : 返回 (logits, layers_data)
        """
        
        img_feat = self.encoder.encode_image(images)  # [B, D]
        #txt_feat = self.encoder.encode_text(texts)    # [B, N, D]
        h = img_feat
        layers_data = []

        for i in range(self.num_layers):
            # Selector
            retrieval_stage = i % 4
            sel_out, sel_info = self.selectors[i](h, self.memory_bank, ppo_mode, deterministic_ppo,retrieval_stage)
            # Attention
            if i==0:
                q_hid = img_feat
            else:
                q_hid = sel_out
            h, attn_info = self.attention_layers[i](
                sel_out, h, h,
                q_hid, ppo_mode, deterministic_ppo
            )
            h = self.norm(h)
            layers_data.append({"selector": sel_info, "attention": attn_info})
            if i > 0:
                h = h + h_prev  
            h_prev = h
        # 与文本特征拼接
        #txt_vec = self.text_proc(txt_feat.view(txt_feat.size(0), -1))
        #print(f"Shape of image_feature: {img_feat.shape}")
        #print(f"Shape of h: {h.shape}")
        final = torch.cat([h, img_feat], dim=-1)

        final = torch.mean(final, dim=1)
        #print(f"Shape of final feature: {final.shape}")
        logits = self.classifier(final)
        if not ppo_mode:
            return logits, None
        B, N, D = img_feat.shape
        sample_trajectory_data = []
        for sample_idx in range(B):
            sample_layers_data = []
            for layer_idx, layer in enumerate(layers_data):
                sample_sel_info = None
                sample_attn_info = None
                if layer["selector"] is not None:
                    start_idx = sample_idx * N
                    end_idx = (sample_idx + 1) * N
                    sample_sel_info = {
                        "state": layer["selector"]["state"][start_idx:end_idx],  # [N, 1024]
                        "actions": {k: v[start_idx:end_idx] for k, v in layer["selector"]["actions"].items()},
                        "log_prob": layer["selector"]["log_prob"][start_idx:end_idx],
                        "value": layer["selector"]["value"][start_idx:end_idx],
                        "selected_features": layer["selector"]["selected_features"][sample_idx],  # [N, k_max, D]
                        "query": layer["selector"]["query"][sample_idx]  # [N, D]
                    }
                if layer["attention"] is not None:
                    sample_attn_info = {
                        "state": layer["attention"]["state"][sample_idx * N:(sample_idx + 1) * N],  # [N, 520]
                        "actions": {k: v[sample_idx * N:(sample_idx + 1) * N] for k, v in layer["attention"]["actions"].items()},
                        "log_prob": layer["attention"]["log_prob"][sample_idx * N:(sample_idx + 1) * N],
                        "value": layer["attention"]["value"][sample_idx * N:(sample_idx + 1) * N]
                    }
                sample_layers_data.append({"selector": sample_sel_info, "attention": sample_attn_info})
            sample_trajectory_data.append(sample_layers_data)
        return logits, sample_trajectory_data,layers_data

# --- 训练主入口 ---
if __name__ == '__main__':
    print("▶ Stage2：PPO 微调开始")

    # SFT 权重路径
    sft_path = "/home/<USER>/RAFT/train_cot_unlabel_rat_Dantopk_imagetext_(4)(6)_4_pool_select1_LayerNorm_classlayer1_gpt_nok1_implicit_concat_512_rnn_setting2.pth"

    # PPOAgent 参数示例
    selector_args = {
        "state_dim": 512*2,
        "action_dims": {"discrete_top_k":5, "continuous_rerank":25},
        "hidden_dim":128,
        "continuous_action_ranges": {"continuous_rerank": (0, 1)}
    }
    attention_args = {
        "state_dim": 520,
        "action_dims": {"continuous_attn_mod":512, "continuous_gate":512},
        "hidden_dim":128,
        "continuous_action_ranges" : {
    "continuous_attn_mod": (-2.0, 2.0),  # 缩放因子 exp(x)
    "continuous_gate": (0.0, 1.0)        # Sigmoid 门控
    }
    }

    # 初始化模型
    model = HierarchicalAttentionModel(
        num_classes=10,
        num_layers=4,
        feat_dim=512,
        sft_weights_path=sft_path,
        selector_ppo_agent_args=selector_args,
        attention_ppo_agent_args=attention_args
    ).to(device)

    # 初始化记忆库
    unlab_loader = DataLoader(share4v_unlabel_dataset(), batch_size=512, shuffle=False)
    model.init_memory_bank(unlab_loader)
    print("✅ Memory bank 构建完成")

    # 有标签训练/验证加载
    train_loader = DataLoader(share4v_train_dataset(), batch_size=32, shuffle=True)
    val_loader   = DataLoader(share4v_val_dataset(),   batch_size=32, shuffle=False)

    # PPO 优化器（仅优化 selector & attention agent）- 降低学习率
    optim_sel  = torch.optim.Adam(
        [p for p in model.parameters() if hasattr(p, 'grad')], lr=3e-6
    )
    optim_attn = torch.optim.Adam(
        [p for p in model.parameters() if hasattr(p, 'grad')], lr=3e-6
    )

    # PPO 超参配置 - 更保守的设置
    ppo_config = {
        "gamma":0.99, "gae_lambda":0.95, "clip_epsilon":0.1,  # 减小clip范围
        "critic_coef":0.5, "entropy_coef":0.01, "max_grad_norm":0.3,  # 更严格的梯度裁剪
        "ppo_epochs":3, "batch_size":32, "buffer_size":200000,  # 减少PPO epochs
        "target_kl":0.01, "novelty_weight":0.1, "entropy_weight":0.1  # 更小的target_kl
    }

    # 运行 PPO 训练
    ppo_training_loop(
        model=model,
        train_dataloader=train_loader,
        val_dataloader=val_loader,
        optimizer_selector=optim_sel,
        optimizer_attention=optim_attn,
        num_epochs=30,
        ppo_config=ppo_config
    )

    print("▶ PPO 微调完成")
