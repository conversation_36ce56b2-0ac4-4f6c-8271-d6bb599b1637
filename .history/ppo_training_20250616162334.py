# ppo_training.py
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import torch.distributions as distributions
import numpy as np
from typing import Dict, List
from ppo_buffer import PPOBuffer,logger,safe_assert
from tqdm import tqdm
# --- 奖励函数与工具函数 ---
import logging
import os
from datetime import datetime




def compute_selector_reward(old_confidence, new_confidence, selected_features, query_features, novelty_weight=0.1):
    confidence_gain = torch.clamp(new_confidence - old_confidence, -2.0, 2.0)  # 进一步限制范围
    assert not torch.isnan(confidence_gain).any(), f"NaN in confidence_gain"
    novel_score = torch.tensor(0.0, device=confidence_gain.device)
    if selected_features.numel() and query_features.numel():
        mean_sel = torch.mean(selected_features, dim=1, keepdim=True)
        # 添加小的epsilon防止除零
        q_norm = F.normalize(query_features.mean(dim=1) + 1e-6, p=2, dim=-1)
        s_norm = F.normalize(mean_sel.squeeze(1) + 1e-6, p=2, dim=-1)
        sim = torch.clamp(F.cosine_similarity(q_norm, s_norm, dim=-1).mean(), -0.95, 0.95)
        novel_score = torch.clamp(1 - sim, -0.5, 0.5)  # 减小novelty score的影响
        assert not torch.isnan(novel_score).any(), f"NaN in novel_score"
    reward = torch.clamp(confidence_gain + novelty_weight * novel_score, -1.0, 1.0)  # 进一步限制最终奖励范围
    safe_assert('selector_reward', reward)
    assert not torch.isnan(reward).any(), f"NaN in selector reward"
    return reward

def compute_attention_reward(# 计算 Attention agent 的奖励
    old_confidence: torch.Tensor,# 旧的分类置信度
    new_confidence: torch.Tensor, # 新的分类置信度
    prediction_probs: torch.Tensor,# 预测的概率分布
    entropy_weight: float = 0.1# 熵权重
) -> torch.Tensor:
    """
    Attention 奖励 = 置信度提升 + 负熵（越低熵越好）
    """
    confidence_gain = torch.clamp(new_confidence - old_confidence, -2.0, 2.0)  # 进一步限制范围
    # 添加小的epsilon防止概率为0
    safe_probs = torch.clamp(prediction_probs, 1e-6, 1.0)
    entropy = distributions.Categorical(probs=safe_probs).entropy()# 计算平均熵
    reward = torch.clamp(confidence_gain + entropy_weight * (-entropy), -1.0, 1.0)  # 进一步限制最终奖励范围
    safe_assert('attention_reward', reward)
    return reward# 奖励 = 置信度提升 - 熵*权重

def get_prediction_confidence_and_probs(logits: torch.Tensor): # 从 logits 获取平均置信度与概率
    """
    从 logits 获取平均置信度和概率分布。
    """
    probs = F.softmax(logits, dim=-1)# softmax 得到概率分布
    confidence, _ = torch.max(probs, dim=-1)# 最大值即置信度
    return confidence, probs # 返回平均置信度和概率分布
def ppo_training_loop(# 主训练循环函数
    model,# 多代理模型
    train_dataloader,# 训练数据加载器
    val_dataloader,# 验证数据加载器
    optimizer_selector,# Selector 优化器
    optimizer_attention,# Attention 优化器
    num_epochs: int, # 训练的总 epoch 数
    ppo_config: Dict# PPO 超参数字典
):
    """
    完整的 PPO 训练循环：先采集经验，再更新策略。
    """
    # 超参
    gamma         = ppo_config["gamma"] # 折扣因子 γ
    gae_lambda    = ppo_config["gae_lambda"]# GAE 的 λ（本文件未直接使用）
    clip_eps      = ppo_config["clip_epsilon"]# PPO 的裁剪阈值 ε
    critic_coef   = ppo_config["critic_coef"]# 值函数损失系数
    entropy_coef  = ppo_config["entropy_coef"] # 熵损失系数
    max_grad_norm = ppo_config["max_grad_norm"] # 梯度裁剪上限
    ppo_epochs    = ppo_config["ppo_epochs"] # 单次更新中 PPO 迭代次数
    batch_size    = ppo_config["batch_size"]# 批量大小
    buffer_size   = ppo_config["buffer_size"]# 缓冲区容量
    target_kl     = ppo_config["target_kl"]# 目标 KL 阈值，用于早停

    device = next(model.parameters()).device# 获取模型所在设备
    num_layers   = model.num_layers# 模型层数（selector/attention 层级）
    agent_types  = ["selector", "attention"] # 代理类型列表

    # 构建 state_dims/action_dims 给 buffer
    state_dims = {
        "selector": model.selectors[0].ppo_agent.actor_base[0].in_features, # selector 状态维度
        "attention": model.attention_layers[0].ppo_agent.actor_base[0].in_features# attention 状态维度
    }
    action_dims = {
        "selector": model.selectors[0].ppo_agent.action_dims, # selector 动作维度
        "attention": model.attention_layers[0].ppo_agent.action_dims# attention 动作维度
    }

    buffer = PPOBuffer(agent_types, num_layers, state_dims, action_dims,# 创建经验缓冲区
                       buffer_size, batch_size, device)

    for epoch in range(num_epochs):# 逐 epoch 训练
        model.train() # 切换到训练模式
        sel_losses, attn_losses = [], []# 记录损失
        best_accuracy = 0.0 # 当前 epoch 的最佳准确率
        for images, texts, labels in tqdm(train_dataloader):# 遍历训练集
            images, labels = images.to(device), labels.to(device)# 将数据搬到 GPU/CPU
            B = images.size(0)
            # --- STEP 1: 采集经验 ---
            with torch.no_grad():# 评估基线，不计算梯度
                base_logits, _ = model(images, texts, ppo_mode=False) # 基线前向，不使用 PPO 动作
                safe_assert('base_logits', base_logits)
                base_conf, base_probs = get_prediction_confidence_and_probs(base_logits)# 获取置信度
                base_loss = F.cross_entropy(base_logits, labels)# 计算基线交叉熵损失

            final_logits, traj_data , layers_data = model(images, texts, ppo_mode=True, deterministic_ppo=False)# 采集 PPO 轨迹
            safe_assert('final_logits', final_logits)
            new_conf, new_probs = get_prediction_confidence_and_probs(final_logits) # PPO 之后的置信度
            new_loss = F.cross_entropy(final_logits, labels)# 计算新损失
            global_reward = base_loss.item() - new_loss.item()# 全局奖励 = 损失减少量

            # 按层存储 selector & attention 的经验
            for sample_idx in range(B):
                sample_traj = traj_data[sample_idx]  # [num_layers, ...]
                sample_base_conf = base_conf[sample_idx]
                sample_new_conf = new_conf[sample_idx]
                sample_new_probs = new_probs[sample_idx:sample_idx+1]
                sample_label = labels[sample_idx:sample_idx+1]
                sample_new_loss = F.cross_entropy(final_logits[sample_idx:sample_idx+1], sample_label)
                sample_global_reward = base_loss.item() - sample_new_loss.item()

                for layer_idx, layer in enumerate(sample_traj):
                    sel = layer.get("selector", None)
                    if sel is not None:
                        reward_sel = compute_selector_reward(
                            sample_base_conf, sample_new_conf,
                            sel["selected_features"], sel["query"],
                            ppo_config["novelty_weight"]
                        )
                        assert not torch.isnan(reward_sel).any(), f"NaN in selector reward, layer {layer_idx}, sample {sample_idx}"
                        reward_sel = 0.7 * reward_sel + 0.3 * sample_global_reward
                        # 为每个 token 的状态存储轨迹
                        for token_idx in range(sel["state"].size(0)):  # N=197
                            buffer.add_trajectory_step(
                                "selector", layer_idx,
                                sel["state"][token_idx:token_idx+1],  # [1, 1024]
                                {k: v[token_idx:token_idx+1] for k, v in sel["actions"].items()},
                                sel["log_prob"][token_idx:token_idx+1],
                                sel["value"][token_idx:token_idx+1],
                                reward_sel / sel["state"].size(0),  # 平均奖励
                                torch.tensor(0.0, device=device)
                            )
                    attn = layer.get("attention", None)
                    if attn is not None:
                        reward_attn = compute_attention_reward(
                            sample_base_conf, sample_new_conf, sample_new_probs,
                            ppo_config["entropy_weight"]
                        )
                        assert not torch.isnan(reward_attn).any(), f"NaN in attention reward, layer {layer_idx}, sample {sample_idx}"
                        #logger.info(f"Reward_sel: min={reward_sel.min().item()}, max={reward_sel.max().item()}, mean={reward_sel.mean().item()}")
                        #logger.info(f"Reward_attn: min={reward_attn.min().item()}, max={reward_attn.max().item()}, mean={reward_attn.mean().item()}")
                        reward_attn = 0.7 * reward_attn + 0.3 * sample_global_reward
                        for token_idx in range(attn["state"].size(0)):  # N=197
                            buffer.add_trajectory_step(
                                "attention", layer_idx,
                                attn["state"][token_idx:token_idx+1],  # [1, 520]
                                {k: v[token_idx:token_idx+1] for k, v in attn["actions"].items()},
                                attn["log_prob"][token_idx:token_idx+1],
                                attn["value"][token_idx:token_idx+1],
                                reward_attn / attn["state"].size(0),  # 平均奖励
                                torch.tensor(0.0, device=device)
                            )
                buffer.finalize_trajectory()
                #logger.info(f"Sample {sample_idx} | reward_sel={reward_sel.item():.4f}, state_shape={sel['state'].shape if sel else 'None'}")
            #logger.info(f"trajectory_count: {buffer.trajectory_count}") 
            #logger.info(f"self.batch_size: {buffer.batch_size}") 
            #logger.info(f"is_ready_for_update: {buffer.is_ready_for_update}")# 记录日志
            # --- STEP 2: 更新策略和值网络 ---
            if buffer.is_ready_for_update: # 当缓冲区满，开始 PPO 更新
                # 更新 Selector
                for layer_idx in range(num_layers): # 遍历层级
                    for agent_type in ["selector", "attention"]:
                        agent = model.selectors[layer_idx].ppo_agent if agent_type == "selector" else model.attention_layers[layer_idx].ppo_agent
                        for name, param in agent.named_parameters():
                            assert not torch.isnan(param).any(), f"NaN in {agent_type} layer {layer_idx} param {name}"
                            #logger.info(f"{agent_type} layer {layer_idx} param {name}: mean={param.mean().item()}, std={param.std().item()}")
                    losses = update_ppo_agent(
                        agent=model.selectors[layer_idx].ppo_agent, # 传入 selector agent
                        optimizer=optimizer_selector,# 对应优化器
                        buffer=buffer,# 经验缓冲区
                        agent_type="selector",# 代理类型
                        layer_idx=layer_idx,# 层索引
                        ppo_epochs=ppo_epochs,# PPO 迭代次数
                        clip_epsilon=clip_eps,# 裁剪系数
                        critic_coef=critic_coef,# 值函数系数
                        entropy_coef=entropy_coef,# 熵系数
                        max_grad_norm=max_grad_norm,# 梯度裁剪
                        target_kl=target_kl,# 目标 KL
                        layers_data=layers_data
                    )
                    sel_losses += losses# 累积 selector 损失

                # 更新 Attention
                for layer_idx in range(num_layers): # 遍历层级
                    losses = update_ppo_agent(
                        agent=model.attention_layers[layer_idx].ppo_agent,# attention agent
                        optimizer=optimizer_attention,# 对应优化器
                        buffer=buffer,# 经验缓冲区
                        agent_type="attention", # 代理类型
                        layer_idx=layer_idx, # 层索引
                        ppo_epochs=ppo_epochs,
                        clip_epsilon=clip_eps,
                        critic_coef=critic_coef,
                        entropy_coef=entropy_coef,
                        max_grad_norm=max_grad_norm,
                        target_kl=target_kl,
                        layers_data=layers_data
                    )
                    attn_losses += losses# 累积 attention 损失

                buffer.clear()# 清空缓冲区，进行下一轮采样

        # 验证集评估
        val_acc, val_loss = evaluate_model(model, val_dataloader)# 在验证集上评估
        #logger.info(f"迭代 {epoch+1}: 损失={val_loss:.4f}, 准确率={val_acc*100:.2f}%")# 记录日志

        # 保存最佳模型
        if val_acc > best_accuracy: # 若本 epoch 更好
            best_accuracy = val_acc# 更新最佳准确率
            torch.save(model.state_dict(), f"best_epoch{epoch+1}.pth")# 保存模型参数
            #logger.info(f"Saved best model @ epoch {epoch+1}  |  acc={val_acc:.4f}") # 打印保存信息


        print(f"Epoch {epoch+1}/{num_epochs}: val_acc={val_acc:.4f}, val_loss={val_loss:.4f}")# 控制台打印进度

def save_checkpoint(self, filename: str):# 保存完整检查点函数
        """保存检查点"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),# 主模型参数
            'agents_state_dict': {agent_id: agent.state_dict() for agent_id, agent in self.agents.items()}, # 所有代理参数
            'optimizers_state_dict': {agent_id: opt.state_dict() for agent_id, opt in self.optimizers.items()},# 各代理优化器
            'main_optimizer_state_dict': self.main_optimizer.state_dict(),# 主优化器状态
        }
        torch.save(checkpoint, filename)# 将字典保存到文件
def update_ppo_agent(# 单个代理的 PPO 更新函数
    agent,# 具体的代理实例
    optimizer,# 对应的优化器
    buffer, # 经验缓冲区
    agent_type: str,# 代理类型（selector/attention）
    layer_idx: int,# 层索引
    ppo_epochs: int,# PPO 迭代次数
    clip_epsilon: float, # 裁剪阈值
    critic_coef: float,# 值函数系数
    entropy_coef: float,# 熵系数
    max_grad_norm: float,# 梯度裁剪上限
    target_kl: float, # 目标 KL
    layers_data
) -> List[float]:
    """
    使用 PPO 算法更新单个 agent。
    """
    for name, param in agent.critic.named_parameters():
        if "bias" in name:
            value = param.data
            mean = param.mean().item()
            std = param.std(unbiased=False).item() if param.numel() > 1 else 0.0
            #logger.info(f"{agent_type} layer {layer_idx} critic {name}: value={value}, mean={mean}, std={std}")
            if torch.isnan(param).any() or torch.isinf(param).any():
                logger.error(f"NaN/Inf in {agent_type} layer {layer_idx} critic {name}: value={value}")
                raise ValueError(f"NaN/Inf in {agent_type} layer {layer_idx} critic {name}")
    losses = []# 存储每次迭代的损失
    batch = buffer.get_batch_data(agent_type, layer_idx) # 从缓冲区获取该层数据
    #batch1=layers_data[layer_idx][agent_type]
    #states, actions = batch1["state"], batch1["actions"]# 状态与动作
    #old_lp, old_val = batch1["log_prob"], batch1["value"] # 旧 log_prob 与旧价值
    states, actions = batch["states"], batch["actions"]# 状态与动作
    old_lp, old_val = batch["old_log_probs"], batch["old_values"] # 旧 log_prob 与旧价值
    advantages, returns = batch["advantages"], batch["returns"] # 优势估计与目标回报
    assert not torch.isnan(old_lp).any(), f"NaN in old_log_probs, {agent_type} layer {layer_idx}"
    assert not torch.isnan(states).any(), f"NaN in states, {agent_type} layer {layer_idx}"
    assert not torch.isnan(advantages).any(), f"NaN in advantages, {agent_type} layer {layer_idx}"
    for epoch in range(ppo_epochs):
        new_actions, new_lp, entropy, new_val = agent.get_action_and_value(states)
        new_val = new_val.squeeze(-1)
        safe_assert('states', states)
        safe_assert('old_lp', old_lp)
        safe_assert('new_lp', new_lp)
        safe_assert('advantages', advantages)
        assert not torch.isnan(new_lp).any(), f"NaN in new_lp, {agent_type} layer {layer_idx}, epoch {epoch}"
        # 更严格的clamp范围防止数值爆炸
        log_ratio = torch.clamp(new_lp - old_lp, -5, 5)  # 进一步减小范围
        ratio = torch.exp(log_ratio)
        safe_assert('ratio', ratio)
        assert not torch.isnan(ratio).any(), f"NaN in ratio, {agent_type} layer {layer_idx}, epoch {epoch}"
        #logger.info(f"Epoch {epoch}, {agent_type} layer {layer_idx}: new_lp_mean={new_lp.mean().item()}, old_lp_mean={old_lp.mean().item()}")
        #logger.info(f"Ratio: min={ratio.min().item()}, max={ratio.max().item()}, mean={ratio.mean().item()}")
        surr1 = ratio * advantages  # 未裁剪目标
        surr2 = torch.clamp(ratio, 1-clip_epsilon, 1+clip_epsilon) * advantages# 裁剪目标
        policy_loss = -torch.min(surr1, surr2).mean()# 策略损失（取最小）
        value_loss = F.mse_loss(new_val, returns)# 值函数均方误差
        entropy_loss = -entropy.mean() # 熵损失（鼓励探索）
        loss = policy_loss + critic_coef * value_loss + entropy_coef * entropy_loss # 总损失
        safe_assert('ppo loss', loss)

        # 检查损失是否为NaN或Inf
        if torch.isnan(loss) or torch.isinf(loss):
            logger.error(f"Invalid loss detected: {loss.item()}, skipping update")
            continue

        losses.append(loss.item()) # 记录损失值

        optimizer.zero_grad() # 梯度清零
        loss.backward() # 反向传播

        # 检查梯度
        total_norm = 0
        for p in agent.parameters():
            if p.grad is not None:
                param_norm = p.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)

        if total_norm > max_grad_norm * 3:  # 更严格的梯度检查阈值
            logger.warning(f"Gradient norm too large: {total_norm}, resetting problematic parameters")
            # 重置梯度过大的参数
            for p in agent.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2).item()
                    if param_norm > max_grad_norm:
                        # 重置这个参数到较小的随机值
                        with torch.no_grad():
                            p.data = torch.randn_like(p.data) * 0.01
            continue

        nn.utils.clip_grad_norm_(agent.parameters(), max_grad_norm)# 梯度裁剪
        optimizer.step() # 更新参数

        # 更稳定的KL散度计算
        with torch.no_grad():
            approx_kl = ((ratio - 1) - log_ratio).mean().abs().item()  # 使用已计算的log_ratio
        if approx_kl > target_kl:# 若 KL 超阈值则提前停止
            print(f"Early stop PPO at KL={approx_kl:.4f}")
            break

    return losses # 返回该代理的损失列表

@torch.no_grad() # 评估时不需要梯度
def evaluate_model(model: nn.Module, dataloader):  # 验证评估函数
    """
    返回 (accuracy, average_loss) – 适用于多类交叉熵分类。
    如果你的任务是 top-k/F1 等，请按需替换指标。
    """
    model.eval()  # 切换到评估模式
    device = next(model.parameters()).device # 获取设备

    total, correct, loss_sum = 0, 0, 0.0 # 初始化指标
    criterion = nn.CrossEntropyLoss() # 定义损失函数

    for images, texts, labels in tqdm(dataloader): # 遍历验证集
        images, labels = images.to(device), labels.to(device) # 数据搬到设备

        logits, _ ,_= model(images, texts, ppo_mode=True,deterministic_ppo=True) # 用确定性策略推理
        loss = criterion(logits, labels) # 计算损失

        preds = logits.argmax(dim=-1)  # 获取预测类别
        correct += (preds == labels).sum().item()# 统计正确数量
        total   += labels.numel()# 统计总数
        loss_sum += loss.item() * labels.size(0) # 累积加权损失

    acc  = correct / total# 准确率
    avg_loss = loss_sum / total# 平均损失
    model.train()          # 别忘了切回训练模式
    return acc, avg_loss # 返回指标
