[06-16 15:50:01] INFO: 成功加载SFT权重: train_cot_unlabel_rat_Dantopk_imagetext_(4)(6)_4_pool_select1_LayerNorm_classlayer1_gpt_nok1_implicit_concat_512_rnn_setting2.pth
[06-16 15:50:01] INFO: 开始初始化记忆库...
[06-16 16:10:38] INFO: ✅ 记忆库初始化完成
[06-16 16:10:38] INFO: 为选择器0启用PPO代理
[06-16 16:10:38] INFO: 为选择器1启用PPO代理
[06-16 16:10:38] INFO: 为选择器2启用PPO代理
[06-16 16:10:38] INFO: 为选择器3启用PPO代理
[06-16 16:10:38] INFO: 为注意力层0启用PPO代理
[06-16 16:10:38] INFO: 为注意力层1启用PPO代理
[06-16 16:10:38] INFO: 为注意力层2启用PPO代理
[06-16 16:10:38] INFO: 为注意力层3启用PPO代理
[06-16 16:10:38] INFO: 总共创建了 8 个PPO代理
[06-16 16:10:38] INFO: 开始多代理PPO训练...
[06-16 16:10:38] INFO: 开始第 1 次多代理PPO迭代
[06-16 16:20:39] INFO: 收集了 10 个episode的多代理经验
[06-16 16:20:39] INFO: 多代理PPO更新完成，平均损失: 0.3746
[06-16 16:20:39] INFO: 开始第 2 次多代理PPO迭代
