# ppo_buffer.py
import torch
import numpy as np
import logging
from typing import Dict, List, Optional

def safe_assert(name, tensor):
    if torch.isnan(tensor).any():
        print(f"NaN detected in {name}, shape={tensor.shape}")
        import pdb; pdb.set_trace()
    if torch.isinf(tensor).any():
        print(f"Inf detected in {name}, shape={tensor.shape}")
        import pdb; pdb.set_trace()
        
def setup_logger(log_file: str = "training.log") -> logging.Logger:# 设置日志的函数，返回 logger 对象
    """
    创建一个写入文件 + 控制台的 logger。
    """
    logger = logging.getLogger("PPO")# 获取名为 "PPO" 的 logger
    logger.setLevel(logging.INFO)# 设置日志等级为 INFO

    fmt = logging.Formatter(# 创建日志格式
        "%(asctime)s | %(levelname)s | %(message)s",# 日志信息包含时间、级别和内容
        datefmt="%Y-%m-%d %H:%M:%S",# 时间格式
    )

    # 文件 handler
    fh = logging.FileHandler(log_file)# 创建文件输出 handler
    fh.setFormatter(fmt) # 设置文件输出格式
    logger.addHandler(fh)# 将文件 handler 添加到 logger

    # 控制台 handler
    ch = logging.StreamHandler()# 创建控制台输出 handler
    ch.setFormatter(fmt) # 设置控制台输出格式
    logger.addHandler(ch)# 将控制台 handler 添加到 logger

    return logger # 返回配置好的 logger
# 初始化 logger
logger = setup_logger("multi_agent_ppo_training.log")
class PPOBuffer:# 定义 PPOBuffer 类
    """
    用于存储 PPO 轨迹的缓冲区，支持多 agent、多层次场景。
    """

    def __init__(
        self,
        agent_types: List[str],# 代理类型列表，如 ["selector", "attention"]
        num_layers: int,# 网络层数，与代理层级一一对应
        state_dims: Dict[str, int], # 每种代理的状态维度
        action_dims: Dict[str, Dict[str, int]],# 每种代理的动作维度（字典形式）
        buffer_size: int = 200000,# 缓冲区容量上限（步数）
        batch_size: int = 32,# 满多少条轨迹后触发更新
        device: torch.device = torch.device("cpu")# 存取数据默认设备
    ):
        self.agent_types = agent_types# 保存代理类型
        self.num_layers = num_layers# 保存层数
        self.state_dims = state_dims# 保存状态维度
        self.action_dims = action_dims# 保存动作维度
        self.buffer_size = buffer_size# 保存缓冲区容量
        self.batch_size = batch_size # 保存批量大小
        self.device = device # 保存设备信息
        self.clear()# 初始化内部存储结构

    def clear(self):# 重置缓冲区数据
        """重置缓冲区数据。"""
        self.trajectories = {# 创建嵌套字典，按 agent→layer 组织
            agent: {
                layer: {
                    "states": [],# 状态序列
                    "actions": {},      # action_name -> list # 动作序列（动作名 → list）
                    "log_probs": [],# 动作 log_prob 序列
                    "values": [],# 价值序列
                    "rewards": [], # 奖励序列
                    "dones": []# done 标记序列
                } for layer in range(self.num_layers)
            } for agent in self.agent_types
        }
        self.trajectory_count = 0# 已完成的轨迹数量计数器
        self.is_ready_for_update = False# 标记缓冲区是否已满可用于更新

    def add_trajectory_step( # 向缓冲区添加一步轨迹
        self,
        agent_type: str, # 代理类型
        layer_idx: int,# 所在层索引
        state: torch.Tensor,# 当前状态张量
        actions: Dict[str, torch.Tensor],# 动作字典（动作名→张量）
        log_prob: torch.Tensor,# 动作对数概率
        value: torch.Tensor,# 价值估计
        reward: Optional[torch.Tensor] = None,# 奖励（可选）
        done: Optional[torch.Tensor] = None# 终止标志（可选）
    ):
        """
        向缓冲区添加一步轨迹。
        """
        traj = self.trajectories[agent_type][layer_idx]
        total_steps = sum(len(traj["states"]) for agent in self.agent_types for layer in range(self.num_layers))
        if total_steps >= self.buffer_size:
            #logger.info(f"Buffer full at {total_steps} steps, skipping add")
            return False
        #print(state.shape)
        safe_assert('buffer state', state)
        safe_assert('buffer log_prob', log_prob)
        safe_assert('buffer value', value)
        state = state.detach().cpu().squeeze(0)  # [1, ...] -> [...]
        log_prob = log_prob.detach().cpu().squeeze(0)
        value = value.detach().cpu().squeeze(0)
        traj["states"].append(state)
        for name, act in actions.items():
            act = act.detach().cpu().squeeze(0)
            traj["actions"].setdefault(name, []).append(act)
        traj["log_probs"].append(log_prob)
        traj["values"].append(value)
        if reward is not None:
            reward = reward.detach().cpu()
            traj["rewards"].append(reward)
        if done is None:
            done = torch.tensor(0.0, device=state.device)
        done = done.detach().cpu()
        traj["dones"].append(done)
        #logger.info(f"Added step for {agent_type} layer {layer_idx} | state_shape={state.shape}")
        return True# 写入成功返回 True

    def add_rewards( # 批量写入 reward（和可选的 done）
        self,
        agent_type: str,# 代理类型
        layer_idx: int,# 层索引
        rewards: torch.Tensor,# 奖励张量
        dones: Optional[torch.Tensor] = None # 可选 done 张量
    ):
        """批量添加 reward（可选 dones）。"""
        rewards = rewards.detach().cpu() # 处理奖励
        self.trajectories[agent_type][layer_idx]["rewards"] = [rewards]# 直接替换奖励列表
        if dones is not None: # 若提供 done
            dones = dones.detach().cpu()# 处理 done
            self.trajectories[agent_type][layer_idx]["dones"] = [dones]# 替换 done 列表

    def finalize_trajectory(self):# 标记一条轨迹结束
        """
        标记所有 agent/layer 的最后一步 done=True，然后计数。
        """
        for agent in self.agent_types:
            for layer in range(self.num_layers):
                traj = self.trajectories[agent][layer]
                if traj["dones"]:
                    traj["dones"][-1] = torch.tensor(1.0)
                elif traj["states"]:
                    traj["dones"].append(torch.tensor(1.0))
        self.trajectory_count += 1
        #logger.info(f"Finalized trajectory, count={self.trajectory_count}")
        if self.trajectory_count >= self.batch_size:
            self.is_ready_for_update = True# 标记可更新

    def compute_advantages_and_returns(# 用 GAE 计算优势值和回报
        self,
        agent_type: str,# 代理类型
        layer_idx: int, # 层索引
        gamma: float = 0.99,# 折扣因子 γ
        gae_lambda: float = 0.95,# GAE λ
        normalize_advantages: bool = True # 是否标准化优势
    ):
        """
        用 GAE 计算 Advantage 和 Return。
        """
        traj = self.trajectories[agent_type][layer_idx] # 取出该层轨迹
        values = torch.stack(traj["values"]) # 拼接 value 序列
        rewards = torch.stack(traj["rewards"])# 拼接 reward 序列
        dones = torch.stack(traj["dones"]) if traj["dones"] else torch.zeros_like(rewards)# 拼接 done 序列

        advantages = torch.zeros_like(rewards) # 初始化优势张量
        returns = torch.zeros_like(rewards) # 初始化回报张量
        next_value = torch.zeros_like(values[0])# 下一状态价值初值
        gae = torch.zeros_like(values[0])# GAE 缓存

        for t in reversed(range(len(rewards))):# 反向遍历时间步
            non_terminal = 1.0 - dones[t]# 是否终止标志（0/1）
            delta = rewards[t] + gamma * next_value * non_terminal - values[t] # TD 残差 δ
            gae = delta + gamma * gae_lambda * non_terminal * gae# 递归计算 GAE
            advantages[t] = gae# 存优势
            returns[t] = gae + values[t] # 计算回报
            next_value = values[t]# 更新下一价值
        #print("here1")
        if normalize_advantages and len(advantages) > 1: # 需要标准化
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8) # 标准化
            #print("here2")
        #print(f"Shape of advantages.mean(): {advantages.mean()}")# 调试打印均值
        #print(f"Shape of advantages.std(): {advantages.std()}")# 调试打印方差
        return advantages, returns# 返回优势与回报

    def get_batch_data(# 整理数据为一次 PPO 更新 batch
        self,
        agent_type: str,# 代理类型
        layer_idx: int,# 层索引     
        gamma: float = 0.99,# 折扣因子 γ
        gae_lambda: float = 0.95,# GAE λ
        normalize_advantages: bool = True# 是否标准化优势
    ):
        """
        将轨迹数据打包为 PPO 更新所需的 batch。
        """
        traj = self.trajectories[agent_type][layer_idx]# 取该层轨迹
        advantages, returns = self.compute_advantages_and_returns(# 先计算优势和回报
            agent_type, layer_idx, gamma, gae_lambda, normalize_advantages
        )
        assert not torch.isnan(advantages).any(), f"NaN in advantages, {agent_type} layer {layer_idx}"
        assert not torch.isnan(returns).any(), f"NaN in returns, {agent_type} layer {layer_idx}"
        #logger.info(f"Advantages: min={advantages.min().item()}, max={advantages.max().item()}, mean={advantages.mean().item()}")
        #print(f"Shape of traj['states']: {traj['states'][0].shape}")
        states = torch.stack(traj["states"]).to(self.device)# 拼接并搬到目标设备
        old_log_probs = torch.stack(traj["log_probs"]).to(self.device) # 拼接 log_prob
        old_values = torch.stack(traj["values"]).to(self.device)# 拼接 value
        actions = {name: torch.stack(lst).to(self.device) for name, lst in traj["actions"].items()}# 拼接动作
        #logger.info(f"states:{states}")
        #logger.info(f"actions:{actions}")

        return { # 返回 batch 字典
            "states": states,# 状态张量
            "actions": actions, # 动作张量
            "old_log_probs": old_log_probs,# 旧 log_prob
            "old_values": old_values, # 旧 value
            "advantages": advantages.to(self.device), # 优势
            "returns": returns.to(self.device)# 回报
        }
